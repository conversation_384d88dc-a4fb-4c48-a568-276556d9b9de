# TrendKurs WordPress Teması

TrendKurs, modern e-ticaret siteler için özel olarak tasarlanmış, Trendyol benzeri kullanıcı arayüzüne sahip, hızlı ve kullanıcı dostu bir WordPress temasıdır.

## 🚀 Özellikler

### ✨ Tasarım
- **Trendyol Benzeri UI**: Modern ve tanıdık kullanıcı arayüzü
- **Responsive Tasarım**: Tüm cihazlarda mükemmel görünüm
- **Hızlı Yükleme**: Optimize edilmiş CSS ve JavaScript
- **Kullanıcı Dostu**: Kolay navigasyon ve sezgisel tasarım

### 🛒 E-ticaret Özellikleri
- **WooCommerce Entegrasyonu**: Tam uyumlu e-ticaret sistemi
- **Ürün Filtreleme**: Gelişmiş filtreleme seçenekleri
- **Hızlı Arama**: AJAX tabanlı anlık arama
- **Sepet İşlemleri**: <PERSON>ız<PERSON><PERSON> sepete ekleme
- **Favori Listesi**: Kullanıcı favori ürünleri
- **Hızlı Görüntüleme**: Ürün detaylarını modal ile görüntüleme

### 📱 Responsive Özellikler
- **Mobil Optimizasyonu**: Mobil cihazlar için özel tasarım
- **Touch Friendly**: Dokunmatik ekranlar için optimize edilmiş
- **Hızlı Performans**: Mobilde de yüksek performans

## 📦 Kurulum

1. **Tema Dosyalarını Yükleyin**
   ```
   wp-content/themes/trendkurs/ klasörüne dosyaları kopyalayın
   ```

2. **WordPress Admin Panelinden Aktifleştirin**
   - Görünüm > Temalar
   - TrendKurs temasını seçin ve aktifleştirin

3. **Gerekli Eklentileri Kurun**
   - WooCommerce (zorunlu)
   - Contact Form 7 (önerilen)

## ⚙️ Yapılandırma

### Menü Ayarları
1. **Görünüm > Menüler**
2. Yeni menü oluşturun
3. "Primary Menu" konumuna atayın

### WooCommerce Ayarları
1. **WooCommerce > Ayarlar**
2. Mağaza sayfalarını yapılandırın
3. Ödeme ve kargo seçeneklerini ayarlayın

### Özelleştirme
1. **Görünüm > Özelleştir**
2. Site kimliği ve renkler
3. Widget alanları

## 🎨 Özelleştirme

### CSS Özelleştirmeleri
Ana CSS dosyası: `/assets/css/trendkurs-main.css`

```css
/* Özel renkler */
:root {
    --primary-color: #ff6000;
    --secondary-color: #e55100;
    --text-color: #333;
    --bg-color: #f8f9fa;
}
```

### JavaScript Özelleştirmeleri
Ana JS dosyası: `/assets/js/trendkurs-main.js`

### PHP Özelleştirmeleri
Ana fonksiyonlar: `functions.php`

## 📋 Sayfa Şablonları

### Ana Sayfa
- `front-page.php`: Özel ana sayfa tasarımı
- Hero banner, kategoriler, öne çıkan ürünler

### Mağaza Sayfaları
- `woocommerce/archive-product.php`: Ürün listesi
- `woocommerce/content-product.php`: Ürün kartları

### Diğer Şablonlar
- `header.php`: Site başlığı
- `footer.php`: Site alt bilgisi
- `index.php`: Genel sayfa şablonu

## 🔧 Teknik Özellikler

### Performans
- **Minified CSS/JS**: Optimize edilmiş dosyalar
- **Lazy Loading**: Gecikmeli resim yükleme
- **AJAX İşlemler**: Sayfa yenilenmeden işlemler
- **Caching Ready**: Önbellekleme uyumlu

### SEO
- **Schema Markup**: Yapılandırılmış veri
- **Meta Tags**: SEO dostu etiketler
- **Clean URLs**: Temiz URL yapısı

### Güvenlik
- **Nonce Verification**: AJAX güvenliği
- **Data Sanitization**: Veri temizleme
- **XSS Protection**: Cross-site scripting koruması

## 🎯 Kullanım Örnekleri

### E-ticaret Mağazası
```php
// Öne çıkan ürünleri göster
$featured_products = wc_get_featured_product_ids();
```

### Kategori Listesi
```php
// Ürün kategorilerini listele
$categories = get_terms( array(
    'taxonomy' => 'product_cat',
    'hide_empty' => true
) );
```

### AJAX Sepet
```javascript
// Sepete ürün ekleme
$('.add-to-cart-btn').on('click', function() {
    // AJAX işlemi
});
```

## 🔄 Güncellemeler

### Versiyon 1.0.0
- İlk sürüm
- Temel e-ticaret özellikleri
- Trendyol benzeri tasarım
- Responsive yapı

## 🆘 Destek

### Sorun Giderme
1. **Tema aktif değil**: WordPress admin panelinden aktifleştirin
2. **WooCommerce çalışmıyor**: WooCommerce eklentisini kurun
3. **Stil sorunları**: Tarayıcı önbelleğini temizleyin

### İletişim
- **E-posta**: <EMAIL>
- **Dokümantasyon**: https://trendkurs.com/docs
- **Forum**: https://trendkurs.com/forum

## 📄 Lisans

Bu tema GNU General Public License v2 veya üzeri ile lisanslanmıştır.

## 🙏 Teşekkürler

- **WordPress**: Güçlü CMS sistemi
- **WooCommerce**: E-ticaret altyapısı
- **Trendyol**: Tasarım ilhamı

---

**TrendKurs Teması** - Modern e-ticaret için tasarlandı ❤️
