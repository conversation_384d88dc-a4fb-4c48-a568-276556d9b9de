/**
 * Demir Customizer Preview JavaScript
 * 
 * Handles live preview functionality for customizer changes
 */

(function($) {
    'use strict';

    // Initialize when customizer preview is ready
    wp.customize.bind('preview-ready', function() {
        
        // Handle color changes
        initColorPreviews();
        
        // Handle typography changes
        initTypographyPreviews();
        
        // Handle layout changes
        initLayoutPreviews();
        
        // Handle general changes
        initGeneralPreviews();
    });

    /**
     * Initialize color live previews
     */
    function initColorPreviews() {
        
        // Primary swatch color
        wp.customize('demir_color_general_swatch', function(value) {
            value.bind(function(newval) {
                updateCSS('.price ins, .summary .yith-wcwl-add-to-wishlist a:before', 'color', newval);
            });
        });

        // General links color
        wp.customize('demir_color_general_links', function(value) {
            value.bind(function(newval) {
                updateCSS('a', 'color', newval);
            });
        });

        // Body background color
        wp.customize('demir_color_body_bg', function(value) {
            value.bind(function(newval) {
                updateCSS('body', 'background-color', newval);
            });
        });

        // Product background color
        wp.customize('demir_product_bg', function(value) {
            value.bind(function(newval) {
                updateCSS('.single-product .site-content .col-full', 'background-color', newval);
            });
        });
    }

    /**
     * Initialize typography live previews
     */
    function initTypographyPreviews() {
        
        // Typography fields that need live preview
        var typographyFields = [
            'demir_typography_h1_fontfamily',
            'demir_typography_h2_fontfamily',
            'demir_typography_h3_fontfamily',
            'demir_typography_h4_fontfamily',
            'demir_typography_h5_fontfamily',
            'demir_typography_h6_fontfamily'
        ];

        typographyFields.forEach(function(fieldBase) {
            // Font family
            wp.customize(fieldBase + '_font_family', function(value) {
                value.bind(function(newval) {
                    var element = getTypographyElement(fieldBase);
                    if (element) {
                        updateCSS(element, 'font-family', newval);
                    }
                });
            });

            // Font size
            wp.customize(fieldBase + '_font_size', function(value) {
                value.bind(function(newval) {
                    var element = getTypographyElement(fieldBase);
                    if (element) {
                        updateCSS(element, 'font-size', newval);
                    }
                });
            });

            // Font weight (variant)
            wp.customize(fieldBase + '_variant', function(value) {
                value.bind(function(newval) {
                    var element = getTypographyElement(fieldBase);
                    if (element) {
                        var fontWeight = newval.replace('italic', '');
                        fontWeight = (fontWeight === '' || fontWeight === 'regular') ? '400' : fontWeight;
                        updateCSS(element, 'font-weight', fontWeight);
                        
                        if (newval.indexOf('italic') !== -1) {
                            updateCSS(element, 'font-style', 'italic');
                        } else {
                            updateCSS(element, 'font-style', 'normal');
                        }
                    }
                });
            });

            // Line height
            wp.customize(fieldBase + '_line_height', function(value) {
                value.bind(function(newval) {
                    var element = getTypographyElement(fieldBase);
                    if (element) {
                        updateCSS(element, 'line-height', newval);
                    }
                });
            });

            // Letter spacing
            wp.customize(fieldBase + '_letter_spacing', function(value) {
                value.bind(function(newval) {
                    var element = getTypographyElement(fieldBase);
                    if (element) {
                        updateCSS(element, 'letter-spacing', newval);
                    }
                });
            });

            // Color
            wp.customize(fieldBase + '_color', function(value) {
                value.bind(function(newval) {
                    var element = getTypographyElement(fieldBase);
                    if (element) {
                        updateCSS(element, 'color', newval);
                    }
                });
            });

            // Text transform
            wp.customize(fieldBase + '_text_transform', function(value) {
                value.bind(function(newval) {
                    var element = getTypographyElement(fieldBase);
                    if (element) {
                        updateCSS(element, 'text-transform', newval);
                    }
                });
            });
        });
    }

    /**
     * Get typography element selector from field name
     */
    function getTypographyElement(fieldBase) {
        var elementMap = {
            'demir_typography_h1_fontfamily': 'h1',
            'demir_typography_h2_fontfamily': 'h2',
            'demir_typography_h3_fontfamily': 'h3',
            'demir_typography_h4_fontfamily': 'h4',
            'demir_typography_h5_fontfamily': 'h5',
            'demir_typography_h6_fontfamily': 'h6'
        };
        
        return elementMap[fieldBase] || null;
    }

    /**
     * Initialize layout live previews
     */
    function initLayoutPreviews() {
        
        // Logo height
        wp.customize('demir_logo_height', function(value) {
            value.bind(function(newval) {
                updateCSS('.site-header .custom-logo-link img', 'height', newval + 'px');
            });
        });

        // Mobile logo height
        wp.customize('demir_mobile_logo_height', function(value) {
            value.bind(function(newval) {
                updateCSSWithMedia(
                    'body.theme-demir .site-header .custom-logo-link img, body.wp-custom-logo .site-header .custom-logo-link img',
                    'height',
                    newval + 'px',
                    '@media (max-width: 992px)'
                );
            });
        });

        // Mobile header height
        wp.customize('demir_mobile_header_height', function(value) {
            value.bind(function(newval) {
                updateCSSWithMedia(
                    '.site-header',
                    'height',
                    newval + 'px',
                    '@media (max-width: 992px)'
                );
            });
        });
    }

    /**
     * Initialize general live previews
     */
    function initGeneralPreviews() {
        
        // Site title
        wp.customize('blogname', function(value) {
            value.bind(function(newval) {
                $('.site-title a').text(newval);
            });
        });

        // Site description
        wp.customize('blogdescription', function(value) {
            value.bind(function(newval) {
                $('.site-description').text(newval);
            });
        });

        // Mobile menu text
        wp.customize('demir_mobile_menu_text', function(value) {
            value.bind(function(newval) {
                $('.bar-text').text(newval);
            });
        });
    }

    /**
     * Update CSS property for given selector
     */
    function updateCSS(selector, property, value) {
        var styleId = 'demir-customizer-' + selector.replace(/[^a-zA-Z0-9]/g, '') + '-' + property;
        var $style = $('#' + styleId);
        
        if ($style.length === 0) {
            $style = $('<style id="' + styleId + '"></style>').appendTo('head');
        }
        
        $style.html(selector + ' { ' + property + ': ' + value + ' !important; }');
    }

    /**
     * Update CSS property with media query
     */
    function updateCSSWithMedia(selector, property, value, mediaQuery) {
        var styleId = 'demir-customizer-media-' + selector.replace(/[^a-zA-Z0-9]/g, '') + '-' + property;
        var $style = $('#' + styleId);
        
        if ($style.length === 0) {
            $style = $('<style id="' + styleId + '"></style>').appendTo('head');
        }
        
        $style.html(mediaQuery + ' { ' + selector + ' { ' + property + ': ' + value + ' !important; } }');
    }

    /**
     * Remove CSS style by ID
     */
    function removeCSS(styleId) {
        $('#' + styleId).remove();
    }

})(jQuery);
