/**
 * TrendKurs Main CSS
 * Modern e-commerce theme inspired by Trendyol design
 * Optimized for speed and user experience
 */

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* ===== HEADER STYLES ===== */
.trendkurs-header {
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-top {
    background: #f1f3f4;
    padding: 8px 0;
    font-size: 13px;
    color: #666;
}

.header-main {
    padding: 15px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.trendkurs-logo {
    font-size: 28px;
    font-weight: bold;
    color: #ff6000;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.trendkurs-logo:hover {
    color: #e55100;
}

/* ===== SEARCH BAR ===== */
.header-search {
    flex: 1;
    max-width: 500px;
    margin: 0 30px;
    position: relative;
}

.search-form {
    display: flex;
    border: 2px solid #ff6000;
    border-radius: 8px;
    overflow: hidden;
}

.search-input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    outline: none;
    font-size: 14px;
}

.search-input::placeholder {
    color: #999;
}

.search-button {
    background: #ff6000;
    color: white;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s ease;
}

.search-button:hover {
    background: #e55100;
}

/* ===== HEADER ACTIONS ===== */
.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-action {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #333;
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.header-action:hover {
    background: #f1f3f4;
    color: #ff6000;
}

.header-action-icon {
    font-size: 20px;
}

.header-action-text {
    font-size: 14px;
    font-weight: 500;
}

.cart-count {
    background: #ff6000;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    position: absolute;
    top: -5px;
    right: -5px;
}

/* ===== NAVIGATION ===== */
.main-navigation {
    background: #fff;
    border-top: 1px solid #e0e0e0;
    padding: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    position: relative;
}

.nav-menu a {
    display: block;
    padding: 15px 20px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-menu a:hover {
    background: #ff6000;
    color: white;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .header-main {
        flex-direction: column;
        gap: 15px;
    }
    
    .header-search {
        margin: 0;
        max-width: 100%;
        width: 100%;
    }
    
    .header-actions {
        justify-content: center;
        width: 100%;
    }
    
    .nav-menu {
        flex-direction: column;
    }
    
    .nav-menu a {
        padding: 12px 15px;
        border-bottom: 1px solid #e0e0e0;
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10px; }
.mb-2 { margin-bottom: 20px; }
.mb-3 { margin-bottom: 30px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10px; }
.mt-2 { margin-top: 20px; }
.mt-3 { margin-top: 30px; }

.btn {
    display: inline-block;
    padding: 12px 24px;
    background: #ff6000;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: background 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background: #e55100;
    color: white;
}

.btn-secondary {
    background: #6c757d;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* ===== LOADING OPTIMIZATION ===== */
.lazy-load {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy-load.loaded {
    opacity: 1;
}

/* ===== ACCESSIBILITY ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
    will-change: transform;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* ===== ADDITIONAL RESPONSIVE STYLES ===== */
@media (max-width: 1024px) {
    .container {
        padding: 0 20px;
    }

    .header-search {
        max-width: 400px;
        margin: 0 20px;
    }

    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)) !important;
        gap: 15px !important;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)) !important;
        gap: 15px !important;
    }
}

@media (max-width: 480px) {
    .header-main {
        padding: 10px 0;
    }

    .trendkurs-logo {
        font-size: 24px;
    }

    .header-search {
        margin: 10px 0;
    }

    .search-input {
        padding: 10px 12px;
        font-size: 14px;
    }

    .search-button {
        padding: 10px 15px;
    }

    .header-actions {
        gap: 10px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .header-action-text {
        display: none;
    }

    .categories-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 10px !important;
    }

    .products-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 10px !important;
    }

    .category-card .category-image,
    .product-card .product-image {
        height: 120px;
    }

    .category-info,
    .product-info {
        padding: 15px !important;
    }

    .hero-content h1 {
        font-size: 28px !important;
    }

    .hero-content p {
        font-size: 16px !important;
    }

    .section-title {
        font-size: 24px !important;
    }

    .newsletter-form {
        flex-direction: column !important;
        gap: 15px !important;
    }
}

/* ===== LOADING STATES ===== */
.loading {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #fff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== NOTIFICATIONS ===== */
.trendkurs-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    display: none;
    max-width: 300px;
}

.trendkurs-notification.success {
    background: #28a745;
}

.trendkurs-notification.error {
    background: #dc3545;
}

.trendkurs-notification.warning {
    background: #ffc107;
    color: #333;
}

/* ===== HOVER EFFECTS ===== */
.category-card:hover,
.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 96, 0, 0.3);
}

/* ===== SCROLLED HEADER ===== */
.trendkurs-header.scrolled {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.trendkurs-header.scrolled .header-main {
    padding: 10px 0;
}

/* ===== SEARCH SUGGESTIONS ===== */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-top: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 1000;
    display: none;
}

.search-suggestions ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.search-suggestions li {
    border-bottom: 1px solid #f0f0f0;
}

.search-suggestions li:last-child {
    border-bottom: none;
}

.search-suggestions a {
    display: block;
    padding: 12px 15px;
    color: #333;
    text-decoration: none;
    transition: background 0.3s ease;
}

.search-suggestions a:hover {
    background: #f8f9fa;
    color: #ff6000;
}
