<?php

/**
 * CommerceGurus Product Filters Plugin
 *
 * @package demir
 * @since demir 1.0.0
 */

/* CommerceGurus Product Filters filters, horizontal layout - include a sticky class when stuck */
add_action( 'woocommerce_after_shop_loop', 'demir_commercegurus_product_filters_horizontal_sticky', 80 );

if ( ! function_exists( 'demir_commercegurus_product_filters_horizontal_sticky' ) ) {
   function demir_commercegurus_product_filters_horizontal_sticky() {

         $demir_layout_woocommerce_sidebar = '';
         $demir_layout_woocommerce_sidebar = demir_get_option( 'demir_layout_woocommerce_sidebar' );

         if ( 'no-woocommerce-sidebar' == $demir_layout_woocommerce_sidebar ) {

         $commercegurus_product_filters_horizontal_sticky_js  = '';
         $commercegurus_product_filters_horizontal_sticky_js .= "
var observer = new IntersectionObserver(function(entries) {
            if(entries[0].intersectionRatio === 0)
               document.querySelector('#cgkitpf-horizontal').classList.add('is-pinned');
            else if(entries[0].intersectionRatio === 1)
               document.querySelector('#cgkitpf-horizontal').classList.remove('is-pinned');
         }, { threshold: [0,1] });

         observer.observe(document.querySelector('.cgkitpf-horizontal-top'));
      ";
         wp_add_inline_script( 'demir-main', $commercegurus_product_filters_horizontal_sticky_js );   

         }   
      ?>
      <?php
   }
}

