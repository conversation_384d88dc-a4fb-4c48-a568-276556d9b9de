<?php
/**
 * The template for displaying product content within loops
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-product.php.
 *
 * @package trendkurs
 */

defined( 'ABSPATH' ) || exit;

global $product;

// Ensure visibility.
if ( empty( $product ) || ! $product->is_visible() ) {
    return;
}
?>

<div <?php wc_product_class( 'trendkurs-product-card', $product ); ?> style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.1); transition: all 0.3s ease; position: relative;">
    
    <!-- Product Image -->
    <div class="product-image-container" style="position: relative; overflow: hidden;">
        
        <?php
        /**
         * Hook: woocommerce_before_shop_loop_item.
         *
         * @hooked woocommerce_template_loop_product_link_open - 10
         */
        do_action( 'woocommerce_before_shop_loop_item' );
        ?>
        
        <div class="product-image" style="height: 250px; background: #f8f9fa; position: relative; overflow: hidden;">
            
            <?php
            /**
             * Hook: woocommerce_before_shop_loop_item_title.
             *
             * @hooked woocommerce_show_product_loop_sale_flash - 10
             * @hooked woocommerce_template_loop_product_thumbnail - 10
             */
            do_action( 'woocommerce_before_shop_loop_item_title' );
            ?>
            
            <!-- Sale Badge -->
            <?php if ( $product->is_on_sale() ) : ?>
                <span class="sale-badge" style="position: absolute; top: 10px; left: 10px; background: #ff6000; color: white; padding: 5px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; z-index: 2;">
                    <?php
                    if ( $product->get_type() === 'variable' ) {
                        echo esc_html__( 'INDIRIM', 'trendkurs' );
                    } else {
                        $regular_price = $product->get_regular_price();
                        $sale_price = $product->get_sale_price();
                        if ( $regular_price && $sale_price ) {
                            $discount = round( ( ( $regular_price - $sale_price ) / $regular_price ) * 100 );
                            echo '-' . $discount . '%';
                        } else {
                            echo esc_html__( 'INDIRIM', 'trendkurs' );
                        }
                    }
                    ?>
                </span>
            <?php endif; ?>
            
            <!-- Quick Actions -->
            <div class="product-actions" style="position: absolute; top: 10px; right: 10px; display: flex; flex-direction: column; gap: 8px; opacity: 0; transition: opacity 0.3s ease;">
                <button class="wishlist-btn" data-product-id="<?php echo esc_attr( $product->get_id() ); ?>" style="background: white; border: none; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    ❤️
                </button>
                <button class="quick-view-btn" data-product-id="<?php echo esc_attr( $product->get_id() ); ?>" style="background: white; border: none; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    👁️
                </button>
            </div>
            
        </div>
        
    </div>
    
    <!-- Product Info -->
    <div class="product-info" style="padding: 20px;">
        
        <!-- Product Title -->
        <h3 class="product-title" style="font-size: 16px; margin-bottom: 10px; color: #333; line-height: 1.4; height: 44px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">
            <?php
            /**
             * Hook: woocommerce_shop_loop_item_title.
             *
             * @hooked woocommerce_template_loop_product_title - 10
             */
            do_action( 'woocommerce_shop_loop_item_title' );
            ?>
        </h3>
        
        <!-- Product Rating -->
        <?php if ( wc_review_ratings_enabled() ) : ?>
            <div class="product-rating" style="margin-bottom: 10px;">
                <?php
                $rating_count = $product->get_rating_count();
                $review_count = $product->get_review_count();
                $average      = $product->get_average_rating();

                if ( $rating_count > 0 ) : ?>
                    <div class="star-rating" style="display: flex; align-items: center; gap: 5px;">
                        <?php echo wc_get_rating_html( $average, $rating_count ); ?>
                        <span style="color: #666; font-size: 12px;">(<?php echo $review_count; ?>)</span>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <!-- Product Price -->
        <div class="product-price" style="margin-bottom: 15px; font-weight: bold;">
            <?php
            /**
             * Hook: woocommerce_after_shop_loop_item_title.
             *
             * @hooked woocommerce_template_loop_rating - 5
             * @hooked woocommerce_template_loop_price - 10
             */
            do_action( 'woocommerce_after_shop_loop_item_title' );
            ?>
        </div>
        
        <!-- Add to Cart Button -->
        <div class="product-add-to-cart">
            <?php
            /**
             * Hook: woocommerce_after_shop_loop_item.
             *
             * @hooked woocommerce_template_loop_product_link_close - 5
             * @hooked woocommerce_template_loop_add_to_cart - 10
             */
            do_action( 'woocommerce_after_shop_loop_item' );
            ?>
        </div>
        
    </div>
    
</div>

<style>
.trendkurs-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.trendkurs-product-card:hover .product-actions {
    opacity: 1;
}

.trendkurs-product-card .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.trendkurs-product-card:hover .product-image img {
    transform: scale(1.05);
}

.trendkurs-product-card .add_to_cart_button,
.trendkurs-product-card .product_type_simple {
    width: 100%;
    background: #ff6000;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.trendkurs-product-card .add_to_cart_button:hover,
.trendkurs-product-card .product_type_simple:hover {
    background: #e55100;
    color: white;
}

.trendkurs-product-card .price {
    color: #ff6000;
    font-size: 18px;
}

.trendkurs-product-card .price del {
    color: #999;
    font-size: 14px;
    margin-right: 8px;
}

.trendkurs-product-card .price ins {
    text-decoration: none;
    color: #ff6000;
}

@media (max-width: 480px) {
    .trendkurs-product-card .product-image {
        height: 200px !important;
    }
    
    .trendkurs-product-card .product-info {
        padding: 15px !important;
    }
    
    .trendkurs-product-card .product-title {
        font-size: 14px !important;
        height: 40px !important;
    }
}
</style>
