<?php
/**
 *
 * <PERSON><PERSON> defaults
 *
 * @package CommerceGurus
 * @subpackage demir
 */

if ( ! function_exists( 'demir_get_option_defaults' ) ) {

	/**
	 *
	 * Sensible defaults ftw.
	 */
	function demir_get_option_defaults() {
		$defaults = array(

			// Top Bar.
			'demir_layout_top_bar_display'           						=> 'enable',
			'demir_layout_top_bar_mobile'            						=> 'hide',

			'demir_layout_top_bar_background'        						=> '#fff',
			'demir_layout_top_bar_text'              						=> '#323232',
			'demir_layout_top_bar_border'            						=> '#eee',

			// Layout.
			'demir_layout_wrapper'										=> 'no',

			// Sidebars.
			'demir_layout_woocommerce_sidebar'       						=> 'left-woocommerce-sidebar',
			'demir_wc_product_category_widget_toggle'						=> 'disable',
			'demir_layout_archives_sidebar'          						=> 'right-archives-sidebar',
			'demir_layout_post_sidebar'              						=> 'right-post-sidebar',
			'demir_layout_page_sidebar'              						=> 'right-page-sidebar',

			// Header.
			'demir_header_layout'											=> 'default',
			'demir_header_layout_container'								=> 'contained',
			'demir_header_bg_color'                  						=> '#fff',
			'demir_header_border_color'              						=> '#eee',
			'demir_layout_search_display'            						=> 'enable',
			'demir_layout_search_display_type'							=> 'default',
			'demir_mobile_hamburger'                 						=> '#111',
			'demir_mobile_cart_color'										=> '#dc9814',
			'demir_mobile_bg'												=> '#fff',
			'demir_mobile_divider_line'              						=> '#eee',
			'demir_mobile_color'                     						=> '#222',
			'demir_sticky_mobile_header'			   						=> 'enable',
			'demir_search_mobile'											=> 'enable',
			'demir_search_mobile_position'								=> 'within-navigation',
			'demir_mobile_myaccount'										=> 'disable',
			'demir_tagline_display'				  						=> false,

			'demir_menu_display_description'         						=> true,

			'demir_layout_woocommerce_cart_icon'     						=> 'basket',

			'demir_layout_myaccount_display'								=> 'disable',

			'demir_layout_search_title'              						=> 'Search',

			'demir_cart_title'											=> 'Your Cart',
			'demir_cart_below_text'										=> '',
			'demir_sidebar_hide_cart_link'								=> false,
			'demir_minicart_quantity'										=> false,

			// Navigation.
			'demir_navigation_bg_color'              						=> '#222',
			'demir_navigation_border_color'		   						=> '',
			'demir_secondary_navigation_color'       						=> '#404040',
			'demir_navigation_color'                 						=> '#fff',
			'demir_navigation_color_header_4'        						=> '#323232',
			'demir_navigation_color_hover'           						=> '#dc9814',
			'demir_menu_hover_intent'										=> false,

			// Navigation Dropdowns.
			'demir_navigation_dropdown_background'   						=> '#fff',
			'demir_navigation_dropdown_color'        						=> '#323232',
			'demir_navigation_dropdown_hover_color'  						=> '#dc9814',

			// Navigation Cart.
			'demir_cart_color'                       						=> '#fff',
			'demir_cart_hover_color'                 						=> '#fff',
			'demir_cart_icon_color'                  						=> '#dc9814',
			'demir_cart_bubble_background_color'							=> '#444444',
			'demir_cart_bubble_border_color'								=> '#444444',

			// Sticky Header.
			'demir_sticky_header'                    						=> 'enable',
			'demir_logo_mark_image'                  						=> '',

			// Below Header.
			'demir_below_header_bg'                  						=> '#dc9814',
			'demir_below_header_text'                						=> '#fff',

			// General
			'demir_layout_woocommerce_breadcrumbs_type' 					=> 'default',

			// Mobile products per row
			'demir_layout_woocommerce_mobile_grid'						=> 'mobile-grid-two',

			// WooCommerce.
			'demir_layout_woocommerce_text_alignment' 					=> 'product-align-left',
			'demir_layout_woocommerce_cta_display'   						=> 'hover',

			'demir_layout_woocommerce_display_cart'  						=> true,

			'demir_layout_woocommerce_single_product_ajax' 				=> false,

			'demir_layout_woocommerce_display_breadcrumbs' 				=> true,
			'demir_layout_shop_title'										=> false,
			'demir_layout_woocommerce_display_count' 						=> true,
			'demir_layout_woocommerce_display_sorting' 					=> true,
			'demir_layout_woocommerce_display_badge' 						=> true,
			'demir_layout_woocommerce_display_badge_type'					=> 'bubble',
			'demir_layout_woocommerce_display_rating' 					=> true,
			'demir_layout_catalog_reviews_count'							=> false,
			'demir_layout_woocommerce_display_category' 					=> true,
			'demir_layout_woocommerce_prev_next_display' 					=> true,
			'demir_layout_woocommerce_sticky_cart_display' 				=> false,
			'demir_layout_woocommerce_related_display' 					=> true,
			'demir_layout_woocommerce_meta_display'  						=> true,

			'demir_layout_woocommerce_card_display'  						=> 'default',
			'demir_layout_woocommerce_flip_image'    						=> false,

			'demir_layout_woocommerce_enable_sidebar_cart' 				=> true,

			'demir_cross_sells_carousel'									=> false,
			'demir_cross_sells_carousel_heading'      					=> 'Pairs well with',

			'demir_layout_floating_button_display'   						=> true,
			'demir_layout_floating_button_text'      						=> 'Questions? Request a Call Back',

			'demir_layout_related_amount'            						=> 4,
			'demir_layout_upsells_amount'            						=> 4,

			'demir_layout_woocommerce_upsells_first' 						=> false,
			'demir_upsells_title_text'               						=> 'Customers also bought',

			'demir_display_cross_sells'									=> true,
			'demir_layout_cross_sells_amount'        						=> 4,

			'demir_layout_pdp_gallery_width'								=> 'wide',

			'demir_layout_pdp_short_description_position'					=> 'top',
			'demir_layout_pdp_block_editor'								=> true,
			'demir_layout_pdp_description_width'							=> 'full-width',
			'demir_widgets_disable_block_editor'							=> true,

			'demir_layout_progress_bar_display'      						=> true,
			'demir_layout_woocommerce_simple_checkout' 					=> true,

			'demir_layout_woocommerce_mobile_cart_page'					=> true,
			'demir_ajaxcart_quantity'										=> false,

			'demir_checkout_coupon_position'								=> 'bottom',

			'demir_layout_woocommerce_sticky_cart_position' 				=> 'bottom',

			'demir_layout_woocommerce_category_position' 					=> 'within-content',
			'demir_layout_woocommerce_category_image' 					=> true,
			'demir_layout_woocommerce_category_description' 				=> true,

			'demir_mobile_menu_text_display'         						=> 'yes',
			'demir_mobile_menu_text'                 						=> 'MENU',

			// Blog.
			'demir_layout_blog'                      						=> 'grid grid-2',
			'demir_layout_blog_title'                      				=> true,
			'demir_layout_blog_summary_display'      						=> true,
			'demir_layout_singlepost'                						=> 'singlepost-layout-one',
			'demir_layout_blog_author'               						=> true,
			'demir_layout_blog_meta'                 						=> true,
			'demir_layout_blog_prev_next'            						=> false,
			'demir_post_featured_image'              						=> true,

			// Colors.
			'demir_color_general_swatch'             						=> '#dc9814',

			'demir_color_general_links'              						=> '#1e68c4',
			'demir_color_general_links_hover'        						=> '#111',

			'demir_color_body_bg'                    						=> '#fff',

			'demir_product_bg'                       						=> '#f8f8f8',

			'demir_ratings_color'                    						=> '#ee9e13',

			'demir_woocommerce_button_text'          						=> '#fff',
			'demir_woocommerce_button_bg'            						=> '#3bb54a',
			'demir_woocommerce_button_hover_bg'      						=> '#009245',

			'demir_sale_flash_bg'                    						=> '#3bb54a',
			'demir_sale_flash_text'                  						=> '#fff',

			'demir_floating_button_bg'               						=> '#dc9814',
			'demir_floating_button_text'             						=> '#fff',

			'demir_archives_description_bg'          						=> '#efeee3',
			'demir_archives_description_text'        						=> '#222',

			'demir_progress_bar_color'               						=> '#3bb54a',

			// Footer.
			'demir_below_content_display'            						=> 'show',
			'demir_footer_display'                   						=> 'show',
			'demir_copyright_display'                						=> 'show',

			'demir_below_content_icons'              						=> '#999',

			'demir_footer_bg'                       						=> '#111',
			'demir_footer_heading_color'             						=> '#fff',
			'demir_footer_color'                     						=> '#ccc',
			'demir_footer_links_color'               						=> '#999',
			'demir_footer_links_hover_color'         						=> '#fff',

			// Speed Settings.
			'demir_general_speed_critical_css'       						=> 'no',
			'demir_general_speed_minify_main_css'    						=> 'yes',
			'demir_general_speed_rivolicons'         						=> 'no',

		);

		return apply_filters( 'demir_get_option_defaults', $defaults );
	}
}// End if().


