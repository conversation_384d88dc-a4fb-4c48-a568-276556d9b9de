<?php
/**
 * demir hooks
 *
 * @package demir
 */

/**
 * General
 *
 * @see  demir_header_widget_region()
 * @see  demir_get_sidebar()
 */
add_action( 'demir_before', 'demir_mobile_menu_close', 10 );
add_action( 'demir_before_content', 'demir_mobile_overlay', 0 );
add_action( 'demir_before_content', 'demir_sticky_header_display', 5 );
add_action( 'demir_before_content', 'demir_header_widget_region', 10 );
add_action( 'demir_sidebar', 'demir_get_sidebar', 10 );

/**
 * Topbar
 *
 * @see  demir_skip_links()
 * @see  demir_top_bar()
 */
add_action( 'demir_before_site', 'demir_skip_links', 0 );
add_action( 'demir_topbar', 'demir_top_bar', 10 );

/**
 * Header
 *
 * @see  demir_skip_links()
 * @see  demir_secondary_navigation()
 * @see  demir_site_branding()
 * @see  demir_primary_navigation()
 * @see  demir_mobile_menu_close()
 */

// Header-4 open wrapper
add_action( 'demir_topbar', 'demir_header_wrapper_open', 90 );

add_action( 'demir_header', 'demir_site_branding', 20 );

$demir_mobile_myaccount = '';
$demir_mobile_myaccount = demir_get_option( 'demir_mobile_myaccount' );

if ( 'enable' === $demir_mobile_myaccount ) {
    add_action( 'demir_header', 'demir_myaccount_icon', 22 );
}

add_action( 'demir_header', 'demir_mobile_search_toggle', 25 );
add_action( 'demir_header', 'demir_secondary_navigation', 30 );
add_action( 'demir_header', 'demir_sticky_js_trigger', 90 );

/**
 * Navigation
 *
 * @see  demir_primary_navigation_wrapper()
 * @see  demir_primary_navigation()
 * @see  demir_primary_navigation_wrapper_close()
 */
add_action( 'demir_navigation', 'demir_primary_navigation_wrapper', 42 );

$demir_search_mobile = '';
$demir_search_mobile = demir_get_option( 'demir_search_mobile' );

add_action( 'demir_navigation', 'demir_primary_navigation', 50 );

add_action( 'demir_navigation', 'demir_mobile_extra_field', 53 );

add_action( 'demir_navigation', 'demir_primary_navigation_wrapper_close', 68 );

// Header-4 close wrapper
add_action( 'demir_before_content', 'demir_header_wrapper_close', 0 );

$demir_header_layout = '';
$demir_header_layout = demir_get_option( 'demir_header_layout' );

$demir_layout_myaccount_display = '';
$demir_layout_myaccount_display = demir_get_option( 'demir_layout_myaccount_display' );

if ( isset( $_GET['header'] ) ) {
	$demir_header_layout = $_GET['header'];
}

if ( 'header-4' === $demir_header_layout ) {
	remove_action( 'demir_navigation', 'demir_primary_navigation_wrapper', 42 );
	remove_action( 'demir_navigation', 'demir_primary_navigation_wrapper_close', 68 );
	add_action( 'demir_navigation', 'demir_search_modal', 50 );

    if ( 'enable' === $demir_layout_myaccount_display ) {
        add_action( 'demir_navigation', 'demir_myaccount_icon', 55 );
    }
}


/**
 * Footer
 *
 * @see  demir_footer_widgets()
 * @see  demir_footer_copyright()
 */
add_action( 'demir_before_footer', 'demir_below_content', 10 );
add_action( 'demir_footer', 'demir_footer_widgets', 20 );
add_action( 'demir_footer', 'demir_footer_copyright', 30 );


/**
 * Posts
 *
 * @see  demir_post_header()
 * @see  demir_post_meta()
 * @see  demir_post_content()
 * @see  demir_paging_nav()
 * @see  demir_single_post_header()
 * @see  demir_post_nav()
 * @see  demir_display_comments()
 */
add_action( 'demir_loop_post', 'demir_post_thumbnail', 5 );
add_action( 'demir_loop_post', 'demir_post_header', 10 );
remove_action( 'demir_loop_post', 'demir_post_content', 30 );

$demir_layout_blog_summary_display = '';
$demir_layout_blog_summary_display = demir_get_option( 'demir_layout_blog_summary_display' );

if ( true === $demir_layout_blog_summary_display ) {
	add_action( 'demir_loop_post', 'demir_archive_post_content', 30 );
}

add_action( 'demir_loop_after', 'demir_paging_nav', 10 );
add_action( 'demir_single_post', 'demir_post_thumbnail_no_link', 5 );
add_action( 'demir_single_post', 'demir_post_header', 10 );
add_action( 'demir_single_post', 'demir_post_content', 30 );
add_action( 'demir_single_post', 'demir_post_meta', 40 );
add_action( 'demir_single_post_bottom', 'demir_display_comments', 20 );



/**
 * Pages
 *
 * @see  demir_page_header()
 * @see  demir_page_content()
 * @see  demir_display_comments()
 */

add_action( 'demir_page_start', 'demir_page_header', 10 );
add_action( 'demir_page', 'demir_page_content', 20 );
add_action( 'demir_page_after', 'demir_display_comments', 10 );
add_action( 'demir_homepage', 'demir_page_content', 20 );
add_action( 'demir_page_sidebar', 'demir_pages_sidebar', 10 );


/**
 * 404 Page.
 */
add_action( 'demir_404_template', 'demir_entry_content_404_page_template');

