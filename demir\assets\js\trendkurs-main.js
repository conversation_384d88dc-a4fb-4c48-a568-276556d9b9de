/**
 * TrendKurs Main JavaScript
 * Modern e-commerce functionality for optimal user experience
 */

(function($) {
    'use strict';

    // DOM Ready
    $(document).ready(function() {
        initTrendKurs();
    });

    /**
     * Initialize TrendKurs functionality
     */
    function initTrendKurs() {
        initSearchFunctionality();
        initMobileMenu();
        initCartFunctionality();
        initLazyLoading();
        initSmoothScrolling();
        initPerformanceOptimizations();
    }

    /**
     * Enhanced Search Functionality
     */
    function initSearchFunctionality() {
        const searchInput = $('.search-input');
        const searchForm = $('.search-form');
        let searchTimeout;

        // Auto-complete functionality
        searchInput.on('input', function() {
            clearTimeout(searchTimeout);
            const query = $(this).val();
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(function() {
                    performSearch(query);
                }, 300);
            }
        });

        // Search form submission
        searchForm.on('submit', function(e) {
            const query = searchInput.val().trim();
            if (query.length < 2) {
                e.preventDefault();
                searchInput.focus();
                showNotification('Lutfen en az 2 karakter girin', 'warning');
            }
        });
    }

    /**
     * Perform AJAX search
     */
    function performSearch(query) {
        // This would integrate with WooCommerce product search
        $.ajax({
            url: trendkurs_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'trendkurs_search_products',
                query: query,
                nonce: trendkurs_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    showSearchSuggestions(response.data);
                }
            }
        });
    }

    /**
     * Mobile Menu Functionality
     */
    function initMobileMenu() {
        const mobileMenuToggle = $('.mobile-menu-toggle');
        const mobileMenu = $('.mobile-menu');
        const body = $('body');

        mobileMenuToggle.on('click', function(e) {
            e.preventDefault();
            body.toggleClass('mobile-menu-open');
            mobileMenu.slideToggle(300);
        });

        // Close menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.mobile-menu, .mobile-menu-toggle').length) {
                body.removeClass('mobile-menu-open');
                mobileMenu.slideUp(300);
            }
        });
    }

    /**
     * Enhanced Cart Functionality
     */
    function initCartFunctionality() {
        // Add to cart with AJAX
        $(document).on('click', '.add-to-cart-btn', function(e) {
            e.preventDefault();
            
            const button = $(this);
            const productId = button.data('product-id');
            const quantity = button.closest('.product').find('.quantity input').val() || 1;

            button.addClass('loading').text('Ekleniyor...');

            $.ajax({
                url: trendkurs_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'trendkurs_add_to_cart',
                    product_id: productId,
                    quantity: quantity,
                    nonce: trendkurs_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        updateCartCount(response.data.cart_count);
                        showNotification('Urun sepete eklendi!', 'success');
                        button.removeClass('loading').text('Sepete Eklendi');
                        
                        // Reset button after 2 seconds
                        setTimeout(function() {
                            button.text('Sepete Ekle');
                        }, 2000);
                    } else {
                        showNotification('Bir hata olustu!', 'error');
                        button.removeClass('loading').text('Sepete Ekle');
                    }
                },
                error: function() {
                    showNotification('Bir hata olustu!', 'error');
                    button.removeClass('loading').text('Sepete Ekle');
                }
            });
        });

        // Update cart count
        function updateCartCount(count) {
            const cartCount = $('.cart-count');
            if (count > 0) {
                cartCount.text(count).show();
            } else {
                cartCount.hide();
            }
        }
    }

    /**
     * Lazy Loading for Images
     */
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy-load');
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(function(img) {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Smooth Scrolling
     */
    function initSmoothScrolling() {
        $('a[href*="#"]:not([href="#"])').on('click', function(e) {
            const target = $(this.getAttribute('href'));
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 500);
            }
        });
    }

    /**
     * Performance Optimizations
     */
    function initPerformanceOptimizations() {
        // Debounce scroll events
        let scrollTimeout;
        $(window).on('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(handleScroll, 10);
        });

        // Debounce resize events
        let resizeTimeout;
        $(window).on('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(handleResize, 100);
        });
    }

    /**
     * Handle scroll events
     */
    function handleScroll() {
        const scrollTop = $(window).scrollTop();
        const header = $('.trendkurs-header');
        
        // Sticky header effect
        if (scrollTop > 100) {
            header.addClass('scrolled');
        } else {
            header.removeClass('scrolled');
        }
    }

    /**
     * Handle resize events
     */
    function handleResize() {
        // Responsive adjustments
        const windowWidth = $(window).width();
        
        if (windowWidth <= 768) {
            $('body').addClass('mobile-view');
        } else {
            $('body').removeClass('mobile-view mobile-menu-open');
            $('.mobile-menu').hide();
        }
    }

    /**
     * Show notification
     */
    function showNotification(message, type) {
        const notification = $('<div class="trendkurs-notification ' + type + '">' + message + '</div>');
        $('body').append(notification);
        
        notification.fadeIn(300);
        
        setTimeout(function() {
            notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    /**
     * Show search suggestions
     */
    function showSearchSuggestions(suggestions) {
        const searchInput = $('.search-input');
        const suggestionsContainer = $('.search-suggestions');
        
        if (suggestions.length > 0) {
            let html = '<ul>';
            suggestions.forEach(function(item) {
                html += '<li><a href="' + item.url + '">' + item.title + '</a></li>';
            });
            html += '</ul>';
            
            suggestionsContainer.html(html).show();
        } else {
            suggestionsContainer.hide();
        }
    }

    /**
     * Product Quick View functionality
     */
    function initQuickView() {
        $(document).on('click', '.quick-view-btn', function(e) {
            e.preventDefault();
            const productId = $(this).data('product-id');
            showQuickViewModal(productId);
        });
    }

    /**
     * Show quick view modal
     */
    function showQuickViewModal(productId) {
        const modal = $('<div class="trendkurs-modal"><div class="modal-content"><div class="modal-loading">Yukleniyor...</div></div></div>');
        $('body').append(modal);
        modal.fadeIn(300);

        $.ajax({
            url: trendkurs_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'trendkurs_quick_view',
                product_id: productId,
                nonce: trendkurs_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    modal.find('.modal-content').html(response.data);
                }
            }
        });

        // Close modal
        $(document).on('click', '.trendkurs-modal, .modal-close', function(e) {
            if (e.target === this) {
                modal.fadeOut(300, function() {
                    $(this).remove();
                });
            }
        });
    }

    /**
     * Newsletter subscription
     */
    function initNewsletterSubscription() {
        $('.newsletter-form').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const email = form.find('input[type="email"]').val();
            const button = form.find('button[type="submit"]');

            if (!isValidEmail(email)) {
                showNotification('Gecerli bir e-posta adresi girin', 'warning');
                return;
            }

            button.addClass('loading').text('Ekleniyor...');

            $.ajax({
                url: trendkurs_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'trendkurs_newsletter_subscribe',
                    email: email,
                    nonce: trendkurs_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        showNotification('Basariyla abone oldunuz!', 'success');
                        form[0].reset();
                    } else {
                        showNotification('Bir hata olustu!', 'error');
                    }
                    button.removeClass('loading').text('Abone Ol');
                },
                error: function() {
                    showNotification('Bir hata olustu!', 'error');
                    button.removeClass('loading').text('Abone Ol');
                }
            });
        });
    }

    /**
     * Validate email address
     */
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Product image zoom functionality
     */
    function initProductImageZoom() {
        $('.product-image').on('mouseenter', function() {
            $(this).addClass('zoom-active');
        }).on('mouseleave', function() {
            $(this).removeClass('zoom-active');
        });
    }

    /**
     * Wishlist functionality
     */
    function initWishlist() {
        $(document).on('click', '.wishlist-btn', function(e) {
            e.preventDefault();

            const button = $(this);
            const productId = button.data('product-id');

            button.addClass('loading');

            $.ajax({
                url: trendkurs_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'trendkurs_toggle_wishlist',
                    product_id: productId,
                    nonce: trendkurs_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        button.toggleClass('active');
                        showNotification(response.data.message, 'success');
                    }
                    button.removeClass('loading');
                }
            });
        });
    }

    // Initialize all functionality
    initTrendKurs();
    initQuickView();
    initNewsletterSubscription();
    initProductImageZoom();
    initWishlist();

})(jQuery);
