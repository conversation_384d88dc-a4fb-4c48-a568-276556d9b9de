<?php
/**
 * demir WooCommerce Class
 *
 * @package  demir
 * <AUTHOR>
 * @since    1.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! class_exists( 'demir_WooCommerce' ) ) :

	/**
	 * The demir WooCommerce Integration class
	 */
	class demir_WooCommerce {

		/**
		 * Setup class.
		 *
		 * @since 1.0
		 */
		public function __construct() {
			add_filter( 'body_class',                               array( $this, 'woocommerce_body_class' ) );
			add_action( 'wp_enqueue_scripts',                       array( $this, 'woocommerce_scripts' ),	20 );
			add_filter( 'woocommerce_enqueue_styles',               '__return_empty_array' );
			add_filter( 'woocommerce_breadcrumb_defaults',          array( $this, 'change_breadcrumb_delimiter' ) );
			$this->load_template_functions();
		}

		/**
		 * Load template function files
		 */
		private function load_template_functions() {
			// Only load if WooCommerce is active
			if ( ! demir_is_woocommerce_activated() ) {
				return;
			}

			if ( demir_is_v2_enabled() ) {
				// Load new modular template functions
				$template_functions = array(
					'global',    // Load global functions first
					'cart',
					'checkout',
					'checkout-flow',
					'mini-cart',
					'breadcrumbs',
					'discount',
					'loop',
					'plp',
					'pdp',
					'search',
					'search-results'
				);

				foreach ($template_functions as $function_file) {
					require_once get_template_directory() . '/inc/woocommerce/template-functions/' . $function_file . '.php';
				}
			} else {
				// Load legacy template functions
				require_once get_template_directory() . '/inc/woocommerce/demir-woocommerce-template-functions.php';
			}
		}

		/**
		 * Remove the breadcrumb delimiter
		 *
		 * @param  array $defaults The breadcrumb defaults
		 * @return array The breadcrumb defaults
		 * @since 1.0.0
		 */
		public function change_breadcrumb_delimiter( $defaults ) {
			$defaults['delimiter'] = '<span class="breadcrumb-separator"> / </span>';
			return $defaults;
		}

		/**
		 * Add 'wc-active' class to the body tag
		 *
		 * @param  array $classes css classes applied to the body tag.
		 * @return array $classes modified to include 'wc-active' class
		 */
		public function woocommerce_body_class( $classes ) {

			$demir_layout_pdp_gallery_width = '';
			$demir_layout_pdp_gallery_width = demir_get_option( 'demir_layout_pdp_gallery_width' );

			$demir_layout_woocommerce_single_product_ajax = '';
			$demir_layout_woocommerce_single_product_ajax = demir_get_option( 'demir_layout_woocommerce_single_product_ajax' );

			$demir_layout_pdp_description_width = '';
			$demir_layout_pdp_description_width = demir_get_option( 'demir_layout_pdp_description_width' );

			if ( demir_is_woocommerce_activated() ) {
				$classes[] = 'wc-active';
			}

			if ( 'skinny' === $demir_layout_pdp_gallery_width ) {
				if ( is_product() ) {
					$classes[] = 'pdp-g-skinny';
				}
			}

			if ( 'regular' === $demir_layout_pdp_gallery_width ) {
				if ( is_product() ) {
					$classes[] = 'pdp-g-regular';
				}
			}

			if ( true === $demir_layout_woocommerce_single_product_ajax ) {
				if ( is_product() ) {
					$classes[] = 'pdp-ajax';
				}
			}

			// Add a class if the PDP description option is set to be full width.
			if ( 'full-width' === $demir_layout_pdp_description_width ) {
				if ( is_product() ) {
					$classes[] = 'pdp-full';
				}
			}

			return $classes;
		}

		/**
		 * WooCommerce specific scripts and stylesheets
		 *
		 * @since 1.0.0
		 */
		public function woocommerce_scripts() {
			global $demir_version;

			$demir_general_speed_minify_main_css = '';
			$demir_general_speed_minify_main_css = demir_get_option( 'demir_general_speed_minify_main_css' );

			$suffix = ( defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG ) ? '' : '.min';
		}

	}

endif;

return new demir_WooCommerce();


