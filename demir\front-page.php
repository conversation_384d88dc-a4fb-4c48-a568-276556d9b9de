<?php
/**
 * TrendKurs Front Page Template
 * 
 * @package trendkurs
 */

get_header(); ?>

<main id="main" class="site-main">

    <!-- Hero Banner Section -->
    <section class="hero-banner">
        <div class="container">
            <div class="hero-slider">
                <div class="hero-slide" style="background: linear-gradient(135deg, #ff6000, #ff8f00); color: white; padding: 60px 0; border-radius: 12px;">
                    <div class="hero-content text-center">
                        <h1 style="font-size: 48px; margin-bottom: 20px; font-weight: bold;">TrendKurs'a Ho<PERSON>diniz</h1>
                        <p style="font-size: 20px; margin-bottom: 30px;">En trend urunler, en uygun fiyatlar!</p>
                        <a href="<?php echo esc_url( wc_get_page_permalink( 'shop' ) ); ?>" class="btn" style="font-size: 18px; padding: 15px 30px;"><PERSON><PERSON><PERSON><PERSON></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="categories-section" style="padding: 60px 0; background: white;">
        <div class="container">
            <h2 class="section-title text-center mb-3" style="font-size: 32px; color: #333;">Populer Kategoriler</h2>
            
            <div class="categories-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 40px;">
                
                <?php
                // Get product categories
                $categories = get_terms( array(
                    'taxonomy' => 'product_cat',
                    'hide_empty' => true,
                    'number' => 8,
                    'parent' => 0
                ) );

                if ( $categories && ! is_wp_error( $categories ) ) :
                    foreach ( $categories as $category ) :
                        $thumbnail_id = get_term_meta( $category->term_id, 'thumbnail_id', true );
                        $image_url = $thumbnail_id ? wp_get_attachment_url( $thumbnail_id ) : wc_placeholder_img_src();
                ?>
                
                <div class="category-card" style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.1); transition: transform 0.3s ease;">
                    <a href="<?php echo esc_url( get_term_link( $category ) ); ?>" style="text-decoration: none; color: inherit;">
                        <div class="category-image" style="height: 150px; background: url('<?php echo esc_url( $image_url ); ?>') center/cover; background-color: #f8f9fa;"></div>
                        <div class="category-info" style="padding: 20px; text-align: center;">
                            <h3 style="font-size: 18px; margin-bottom: 5px; color: #333;"><?php echo esc_html( $category->name ); ?></h3>
                            <span style="color: #666; font-size: 14px;"><?php echo $category->count; ?> urun</span>
                        </div>
                    </a>
                </div>
                
                <?php endforeach; endif; ?>
                
            </div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section class="featured-products" style="padding: 60px 0; background: #f8f9fa;">
        <div class="container">
            <h2 class="section-title text-center mb-3" style="font-size: 32px; color: #333;">One Cikan Urunler</h2>
            
            <div class="products-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 40px;">
                
                <?php
                // Get featured products
                $featured_products = wc_get_featured_product_ids();
                $products_query = new WP_Query( array(
                    'post_type' => 'product',
                    'posts_per_page' => 8,
                    'post__in' => $featured_products,
                    'meta_query' => array(
                        array(
                            'key' => '_visibility',
                            'value' => array( 'catalog', 'visible' ),
                            'compare' => 'IN'
                        )
                    )
                ) );

                if ( $products_query->have_posts() ) :
                    while ( $products_query->have_posts() ) : $products_query->the_post();
                        global $product;
                ?>
                
                <div class="product-card" style="background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.1); transition: transform 0.3s ease;">
                    <a href="<?php the_permalink(); ?>" style="text-decoration: none; color: inherit;">
                        <div class="product-image" style="height: 200px; background: url('<?php echo esc_url( wp_get_attachment_url( $product->get_image_id() ) ); ?>') center/cover; background-color: #f8f9fa; position: relative;">
                            <?php if ( $product->is_on_sale() ) : ?>
                                <span class="sale-badge" style="position: absolute; top: 10px; left: 10px; background: #ff6000; color: white; padding: 5px 10px; border-radius: 20px; font-size: 12px; font-weight: bold;">INDIRIM</span>
                            <?php endif; ?>
                        </div>
                        <div class="product-info" style="padding: 20px;">
                            <h3 style="font-size: 16px; margin-bottom: 10px; color: #333; line-height: 1.4;"><?php the_title(); ?></h3>
                            <div class="product-price" style="margin-bottom: 15px;">
                                <?php echo $product->get_price_html(); ?>
                            </div>
                            <button class="btn add-to-cart-btn" data-product-id="<?php echo $product->get_id(); ?>" style="width: 100%; background: #ff6000; color: white; border: none; padding: 12px; border-radius: 6px; font-weight: 500; cursor: pointer;">
                                Sepete Ekle
                            </button>
                        </div>
                    </a>
                </div>
                
                <?php endwhile; wp_reset_postdata(); endif; ?>
                
            </div>
            
            <div class="text-center mt-3">
                <a href="<?php echo esc_url( wc_get_page_permalink( 'shop' ) ); ?>" class="btn btn-secondary" style="background: #6c757d; padding: 15px 30px;">Tum Urunleri Gor</a>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="newsletter-section" style="padding: 60px 0; background: linear-gradient(135deg, #ff6000, #ff8f00); color: white;">
        <div class="container text-center">
            <h2 style="font-size: 32px; margin-bottom: 20px;">Kampanyalardan Haberdar Ol</h2>
            <p style="font-size: 18px; margin-bottom: 30px;">En yeni urunler ve ozel indirimler icin e-bultene abone ol!</p>
            
            <form class="newsletter-form" style="max-width: 400px; margin: 0 auto; display: flex; gap: 10px;">
                <input type="email" placeholder="E-posta adresiniz" style="flex: 1; padding: 15px; border: none; border-radius: 6px; font-size: 16px;" required>
                <button type="submit" class="btn" style="background: white; color: #ff6000; padding: 15px 25px; border: none; border-radius: 6px; font-weight: bold;">Abone Ol</button>
            </form>
        </div>
    </section>

</main>

<?php get_footer(); ?>
