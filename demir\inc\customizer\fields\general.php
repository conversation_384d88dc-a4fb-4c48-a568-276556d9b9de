<?php
/**
 *
 * General theme options
 *
 * @package CommerceGurus
 * @subpackage demir
 */

// General fields.
$demir_default_options = demir_get_option_defaults();

// Header Logo Height.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_logo_height',
		'label'       => esc_html__( 'Logo height', 'demir' ),
		'description' => esc_html__( 'Adjust the height of your logo in pixels. You can upload your logo image within the "Site Identity" panel.', 'demir' ),
		'section'     => 'demir_section_general_logo',
		'default'     => 38,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 300,
			'step' => 1,
		),
		'active_callback'  => [
			[
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			],
		],	
		'output'      => array(
			array(
				'element'  => '.site-header .custom-logo-link img',
				'property' => 'height',
				'units'    => 'px',
			),
		),
	)
);

// Header 4 (One row) Logo Height.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_logo_height_header4',
		'label'       => esc_html__( 'Logo height', 'demir' ),
		'description' => esc_html__( 'Adjust the height of your logo in pixels. You can upload your logo image within the "Site Identity" panel.', 'demir' ),
		'section'     => 'demir_section_general_logo',
		'default'     => 30,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 300,
			'step' => 1,
		),
		'active_callback'  => [
			[
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '==',
			],
		],	
		'output'      => array(
			array(
				'element'  => '.header-4 .site-header .custom-logo-link img',
				'property' => 'height',
				'units'    => 'px',
			),
		),
	)
);

// Display tagline under logo.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'toggle',
		'settings' => 'demir_tagline_display',
		'label'    => esc_attr__( 'Display tagline under logo', 'demir' ),
		'description'    => esc_attr__( 'This is set within Settings > General', 'demir' ),
		'section'  => 'demir_section_general_logo',
		'default'  => $demir_default_options['demir_tagline_display'],
		'priority'  => 10,
		'transport' => 'refresh',
	)
);


// Sticky Logo Image.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'image',
		'settings' => 'demir_logo_mark_image',
		'label'    => esc_html__( 'Sticky logo', 'demir' ),
		'section'  => 'demir_section_general_sticky_logo',
		'default'  => $demir_default_options['demir_logo_mark_image'],
		'priority' => 10,
		'active_callback'  => array(
			array(
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			),
		),
	)
);


// Sticky Logo Image Width.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'slider',
		'settings' => 'demir_sticky_logo_width',
		'label'    => esc_html__( 'Sticky logo width', 'demir' ),
		'description'    => esc_attr__( 'Suggested width of at least 60', 'demir' ),
		'section'  => 'demir_section_general_sticky_logo',
		'default'  => 60,
		'priority' => 10,
		'active_callback'  => array(
			array(
				'setting'  => 'demir_header_layout',
				'value'    => 'header-4',
				'operator' => '!=',
			),
		),
		'choices'  => array(
			'min'  => 0,
			'max'  => 300,
			'step' => 1,
		),
		'output'   => array(
			array(
				'element'  => '.is_stuck .logo-mark',
				'property' => 'width',
				'units'    => 'px',
			),
			array(
				'element'  => '.is_stuck .primary-navigation.with-logo .menu-primary-menu-container',
				'property' => 'margin-left',
				'units'    => 'px',
				'media_query'   => '@media (min-width: 993px)',
			),
		),
	)
);

// Mobile Header Height.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_mobile_header_height',
		'label'       => esc_html__( 'Mobile header height', 'demir' ),
		'description' => esc_html__( 'Adjust height of your mobile header (px)', 'demir' ),
		'section'     => 'demir_section_general_mobile_header',
		'default'     => 70,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 200,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'     => '.main-header, .site-branding',
				'property'    => 'height',
				'units'       => 'px',
				'media_query' => '@media (max-width: 992px)',
			),
			array(
				'element'       => '.main-header .site-header-cart',
				'value_pattern' => 'calc(-14px + $px / 2)',
				'property'      => 'top',
				'units'         => '',
				'media_query'   => '@media (max-width: 992px)',
			),
			array(
				'element'       => '.sticky-m .mobile-filter, .sticky-m #cgkitpf-horizontal',
				'property'      => 'top',
				'units'         => 'px',
				'media_query'   => '@media (max-width: 992px)',
			),
			array(
				'element'     => '.sticky-m .commercekit-atc-sticky-tabs',
				'value_pattern' => 'calc($px - 1px)',
				'property'    => 'top',
				'media_query' => '@media (max-width: 992px)',
			),
			array(
				'element'     => '.m-search-bh.sticky-m .commercekit-atc-sticky-tabs, .m-search-toggled.sticky-m .commercekit-atc-sticky-tabs',
				'value_pattern' => 'calc($px + 60px - 1px)',
				'property'    => 'top',
				'media_query' => '@media (max-width: 992px)',
			),
			array(
				'element'     => '.m-search-bh.sticky-m .mobile-filter, .m-search-toggled.sticky-m .mobile-filter, .m-search-bh.sticky-m #cgkitpf-horizontal, .m-search-toggled.sticky-m #cgkitpf-horizontal',
				'value_pattern' => 'calc($px + 60px)',
				'property'    => 'top',
				'media_query' => '@media (max-width: 992px)',
			),
			array(
				'element'     => '.sticky-m .cg-layout-vertical-scroll .cg-thumb-swiper',
				'value_pattern' => 'calc($px + 10px)',
				'property'    => 'top',
				'media_query' => '@media (max-width: 992px)',
			),

		),
	)
);

// Mobile Logo Height.
demir_Kirki::add_field(
	'demir_config', array(
		'type'        => 'slider',
		'settings'    => 'demir_mobile_logo_height',
		'label'       => esc_html__( 'Mobile logo height', 'demir' ),
		'description' => esc_html__( 'Adjust height of your mobile logo (px)', 'demir' ),
		'section'     => 'demir_section_general_mobile_header',
		'default'     => 22,
		'priority'    => 1,
		'choices'     => array(
			'min'  => 0,
			'max'  => 100,
			'step' => 1,
		),
		'output'      => array(
			array(
				'element'     => 'body.theme-demir .site-header .custom-logo-link img,
				body.wp-custom-logo .site-header .custom-logo-link img',
				'property'    => 'height',
				'units'       => 'px',
				'media_query' => '@media (max-width: 992px)',
			),
		),
	)
);

// Mobile Sticky Header.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'select',
		'settings' => 'demir_sticky_mobile_header',
		'label'    => esc_attr__( 'Mobile sticky header', 'demir' ),
		'section'  => 'demir_section_general_mobile_header',
		'default'  => $demir_default_options['demir_sticky_mobile_header'],
		'choices'  => array(
			'enable' => esc_attr__( 'Enable', 'demir' ),
			'disable'  => esc_attr__( 'Disable', 'demir' ),

		),
		'priority' => 10,
	)
);

// Display Search on Mobile.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_search_mobile',
		'label'     => esc_html__( 'Show search on mobile', 'demir' ),
		'section'   => 'demir_section_general_mobile_header',
		'default'  => $demir_default_options['demir_search_mobile'],
		'choices'  => array(
			'enable' => esc_attr__( 'Enable', 'demir' ),
			'disable'  => esc_attr__( 'Disable', 'demir' ),

		),
		'priority' => 10,
	)
);

// Mobile Search Position.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_search_mobile_position',
		'label'     => esc_html__( 'Mobile search position', 'demir' ),
		'section'   => 'demir_section_general_mobile_header',
		'active_callback'  => array(
			array(
				'setting'  => 'demir_search_mobile',
				'value'    => 'enable',
				'operator' => '==',
			),
		),
		'choices'  => array(
			'within-navigation' => esc_attr__( 'Within navigation', 'demir' ),
			'below-header'  => esc_attr__( 'Below header bar', 'demir' ),
			'toggle'  => esc_attr__( 'Header icon and toggle', 'demir' ),
		),
		'priority' => 10,
	)
);

// Display My Account on Mobile.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'select',
		'settings'  => 'demir_mobile_myaccount',
		'label'     => esc_html__( 'Show my account on mobile', 'demir' ),
		'section'   => 'demir_section_general_mobile_header',
		'default'  => $demir_default_options['demir_mobile_myaccount'],
		'choices'  => array(
			'enable' => esc_attr__( 'Enable', 'demir' ),
			'disable'  => esc_attr__( 'Disable', 'demir' ),

		),
		'priority' => 10,
	)
);

// Display Mobile Menu label.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'select',
		'settings' => 'demir_mobile_menu_text_display',
		'label'    => esc_attr__( 'Display mobile menu label', 'demir' ),
		'section'  => 'demir_section_general_mobile_header',
		'default'  => $demir_default_options['demir_mobile_menu_text_display'],
		'choices'  => array(
			'yes' => esc_attr__( 'Yes', 'demir' ),
			'no'  => esc_attr__( 'No', 'demir' ),

		),
		'priority' => 10,
	)
);

// Mobile Menu label text.
demir_Kirki::add_field(
	'demir_config', array(
		'type'      => 'text',
		'settings'  => 'demir_mobile_menu_text',
		'label'     => esc_html__( 'Mobile menu label text', 'demir' ),
		'section'   => 'demir_section_general_mobile_header',
		'default'   => $demir_default_options['demir_mobile_menu_text'],
		'priority'  => 10,
		'transport' => 'auto',
		'active_callback'  => array(
			array(
				'setting'  => 'demir_mobile_menu_text_display',
				'value'    => 'yes',
				'operator' => '==',
			),
		),
		'js_vars'   => array(
			array(
				'element'  => '.bar-text',
				'function' => 'html',
			),
		),
	)
);

// Critical CSS Settings.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_general_speed_heading_1',
		'section'  => 'demir_section_general_speed_settings',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Critical CSS', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Critical CSS.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'select',
		'settings' => 'demir_general_speed_critical_css',
		'label'    => esc_attr__( 'Enable critical CSS?', 'demir' ),
		'section'  => 'demir_section_general_speed_settings',
		'default'  => 'no',
		'choices'  => array(
			'yes' => esc_attr__( 'Yes', 'demir' ),
			'no'  => esc_attr__( 'No', 'demir' ),

		),
		'priority' => 10,
	)
);


// Minification Settings.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_general_speed_heading_2',
		'section'  => 'demir_section_general_speed_settings',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Minification Settings', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Main CSS Minified.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'select',
		'settings' => 'demir_general_speed_minify_main_css',
		'label'    => esc_attr__( 'Load minified CSS files?', 'demir' ),
		'section'  => 'demir_section_general_speed_settings',
		'default'  => 'yes',
		'choices'  => array(
			'yes' => esc_attr__( 'Yes', 'demir' ),
			'no'  => esc_attr__( 'No', 'demir' ),
		),
		'priority' => 10,
	)
);

// Icon Font.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'custom',
		'settings' => 'demir_general_speed_heading_3',
		'section'  => 'demir_section_general_speed_settings',
		'default'  => '<div class="kirki-separator" style="margin: 10px -12px; padding: 12px 12px; color: #111; text-transform: uppercase;
	letter-spacing: 1px; border-top: 1px solid #ddd; border-bottom: 1px solid #ddd; background-color: #fff; cursor: default;">' . esc_html__( 'Icon Font', 'demir' ) . '</div>',
		'priority' => 10,
	)
);

// Rivolicons.
demir_Kirki::add_field(
	'demir_config', array(
		'type'     => 'select',
		'settings' => 'demir_general_speed_rivolicons',
		'label'    => esc_attr__( 'Load Rivolicons icon font?', 'demir' ),
		'section'  => 'demir_section_general_speed_settings',
		'default'  => 'no',
		'choices'  => array(
			'yes' => esc_attr__( 'Yes', 'demir' ),
			'no'  => esc_attr__( 'No', 'demir' ),
		),
		'priority' => 10,
	)
);



