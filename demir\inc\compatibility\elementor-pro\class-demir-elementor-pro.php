<?php
/**
 * Elementor Compatibility File.
 *
 * @package demir
 */

namespace Elementor;

// If plugin - 'Elementor' not exist then return.
if ( ! class_exists( '\Elementor\Plugin' ) || ! class_exists( 'ElementorPro\Modules\ThemeBuilder\Module' ) ) {
	return;
}

namespace ElementorPro\Modules\ThemeBuilder\ThemeSupport;

use Elementor\TemplateLibrary\Source_Local;
use ElementorPro\Modules\ThemeBuilder\Classes\Locations_Manager;
use ElementorPro\Modules\ThemeBuilder\Module;

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * demir Elementor Compatibility
 */
if ( ! class_exists( 'demir_Elementor_Pro' ) ) :

	/**
	 * demir Elementor Compatibility
	 *
	 * @since 1.8.0
	 */
	class demir_Elementor_Pro {

		/**
		 * Member Variable
		 *
		 * @var object instance
		 */
		private static $instance;

		/**
		 * Initiator
		 *
		 * @since 1.8.0
		 * @return object Class object.
		 */
		public static function get_instance() {
			if ( ! isset( self::$instance ) ) {
				self::$instance = new self();
			}
			return self::$instance;
		}

		/**
		 * Constructor
		 *
		 * @since 1.8.0
		 */
		public function __construct() {

			// Add locations.
			add_action( 'elementor/theme/register_locations', array( $this, 'register_locations' ) );

			// Override theme templates.
			add_action( 'demir_single_post', array( $this, 'do_template_parts' ), 0 );
			add_action( 'demir_page_start', array( $this, 'do_template_parts' ), 0 );
			add_action( 'demir_page', array( $this, 'do_template_parts' ), 0 );
			add_action( 'demir_page_after', array( $this, 'do_template_parts' ), 0 );

			add_action( 'demir_404_template', array( $this, 'do_template_part_404' ), 0 );

			// Header
			add_action( 'demir_topbar', array( $this, 'do_header' ), 0 );
			add_action( 'demir_header', array( $this, 'do_header' ), 0 );

			// Footer
			add_action( 'demir_before_footer', array( $this, 'do_footer' ), 0 );
			add_action( 'demir_footer', array( $this, 'do_footer' ), 0 );

		}

		/**
		 * Register Locations
		 *
		 * @since 1.8.0
		 * @param object $manager Location manager.
		 * @return void
		 */
		public function register_locations( $manager ) {
			$manager->register_all_core_location();
		}

		/**
		 * Template Parts Support
		 *
		 * @since 1.9.8
		 * @return void
		 */
		function do_template_parts() {
			// Is a single post or page?
			$did_location = Module::instance()->get_locations_manager()->do_location( 'single' );
			if ( $did_location ) {
				remove_action( 'demir_single_post', 'demir_post_thumbnail_no_link', 5 );
				remove_action( 'demir_single_post', 'demir_post_header', 10 );
				remove_action( 'demir_single_post', 'demir_post_content', 30 );
				remove_action( 'demir_single_post', 'demir_post_meta', 40 );
				remove_action( 'demir_single_post_bottom', 'demir_display_comments', 20 );
				remove_action( 'demir_page_start', 'demir_page_header', 10 );
				remove_action( 'demir_page', 'demir_page_content', 20 );
				remove_action( 'demir_page_after', 'demir_display_comments', 10 );
			}
		}

		/**
		 * Header Support
		 *
		 * @since 1.8.0
		 * @return void
		 */
		public function do_header() {

			$demir_header_layout = '';
			$demir_header_layout = demir_get_option( 'demir_header_layout' );

			$did_location = Module::instance()->get_locations_manager()->do_location( 'header' );
			if ( $did_location ) {
				remove_action( 'demir_topbar', 'demir_top_bar', 10 );
				remove_action( 'demir_header', 'demir_site_branding', 20 );
				remove_action( 'demir_header', 'demir_product_search', 25 );
				remove_action( 'demir_header', 'demir_secondary_navigation', 30 );
				remove_action( 'demir_header', 'demir_header_cart', 50 );
				remove_action( 'demir_header', 'demir_sticky_js_trigger', 90 );

				remove_action( 'demir_navigation', 'demir_primary_navigation_wrapper', 42 );
				remove_action( 'demir_navigation', 'demir_primary_navigation', 50 );
				remove_action( 'demir_navigation', 'demir_header_cart', 60 );
				remove_action( 'demir_navigation', 'demir_primary_navigation_wrapper_close', 68 );
				remove_action( 'demir_navigation', 'demir_header_wrapper_close', 75 );

				if ( 'header-4' === $demir_header_layout ) {
					remove_action( 'demir_header', 'demir_header_wrapper_open', 10 );
					remove_action( 'demir_header', 'demir_header_cart', 50 );
					remove_action( 'demir_navigation', 'demir_primary_navigation_wrapper', 42 );
					remove_action( 'demir_navigation', 'demir_primary_navigation', 50 );
					remove_action( 'demir_navigation', 'demir_primary_navigation_wrapper_close', 68 );
					remove_action( 'demir_navigation', 'demir_header_wrapper_close', 75 );
					remove_action( 'demir_navigation', 'demir_search_modal', 50 );
					remove_action( 'demir_navigation', 'demir_header_cart', 60 );
				}

				//Solves issues when replacing menu with Elementor Pro's.
				remove_action( 'demir_header', 'demir_menu_load_shortcode', 42 );

			}
		}

		/**
		 * Override 404 page
		 *
		 * @since 2.3.6
		 * @return void
		 */
		public function do_template_part_404() {
			if ( is_404() ) {

				// Is Single?
				$did_location = Module::instance()->get_locations_manager()->do_location( 'single' );
				if ( $did_location ) {
					remove_action( 'demir_404_template', 'demir_entry_content_404_page_template');
				}
			}
		}

		/**
		 * Footer Support
		 *
		 * @since 1.8.0
		 * @return void
		 */
		public function do_footer() {
			$did_location = Module::instance()->get_locations_manager()->do_location( 'footer' );
			if ( $did_location ) {
				remove_action( 'demir_before_footer', 'demir_below_content', 10 );
 				remove_action( 'demir_footer', 'demir_footer_widgets', 20 );
				remove_action( 'demir_footer', 'demir_footer_copyright', 30 );
			}
		}


		}

	demir_Elementor_Pro::get_instance();

endif;



