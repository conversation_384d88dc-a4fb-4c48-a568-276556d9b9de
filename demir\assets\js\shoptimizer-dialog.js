document.addEventListener('DOMContentLoaded', function() {
  const dialogs = document.querySelectorAll('dialog.demir-modal');
  var demirFElements = getFocusableElements();
  var demirFFElement = demirFElements[0];
  var demirLFElement = demirFElements[demirFElements.length - 1];
  
  document.addEventListener('click', event => {
    const demirtrigger = event.target.dataset.trigger;
    if (demirtrigger) {
      const modalId = demirtrigger;
      const modalElement = document.querySelector(`[data-demirmodal-id="${modalId}"]`);
      if (modalElement) {
        closeAllDialogs();
        if (modalId === 'searchToggle') {
          modalElement.show();
          updateFocusableElements();
          trapSearchToggleModal(modalElement);
        } else {
          modalElement.showModal();
        }
      }
    }
  });
  
  dialogs.forEach(demirdialog => {
    demirdialog.addEventListener('click', function(event) {
      if (event.target === demirdialog) {
        closeDialog(demirdialog);
      }
      if (event.target.closest('.demir-modal--button_close')) {
        event.preventDefault();
        closeDialog(demirdialog);
      }
    });
    
    // Add keydown event listener for ESC key
    demirdialog.addEventListener('keydown', function(event) {
      if (event.key === 'Escape') {
        closeDialog(demirdialog);
      }
    });
  });
  
  function closeAllDialogs() {
    dialogs.forEach(dialog => {
      if (dialog.open) {
        dialog.close();
      }
    });
  }
  
  function closeDialog(dialog) {
    dialog.close();
  }
  
  function getFocusableElements() {
    var modalElm = document.querySelector('[data-demirmodal-id="searchToggle"]');
    if (modalElm) {
      return modalElm.querySelectorAll('a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select');
    } else {
      return new Array();
    }
  }
  
  function updateFocusableElements() {
    demirFElements = getFocusableElements();
    demirFFElement = demirFElements[0];
    demirLFElement = demirFElements[demirFElements.length - 1];
  }
  
  function trapSearchToggleModal(element) {
    demirFFElement.focus();
    
    element.addEventListener('keydown', function(e) {
      let isTabPressed = e.key === 'Tab' || e.keyCode === 9;
      if (!isTabPressed) {
        return;
      }
      if (e.shiftKey) { 
        if (document.activeElement === demirFFElement) {
          demirLFElement.focus();
          e.preventDefault();
        }
      } else { 
        if (document.activeElement === demirLFElement) { 
          demirFFElement.focus(); 
          e.preventDefault();
        }
      }
    });
  }
  
  var modalContent = document.querySelector('[data-demirmodal-id="searchToggle"]');
  if (modalContent) {
    var modalObserver = new MutationObserver(() => {
      updateFocusableElements();
    });
    modalObserver.observe(modalContent, { childList: true, subtree: true });
  }
});
