#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: \n"
"POT-Creation-Date: 2021-07-15 17:22+0100\n"
"PO-Revision-Date: 2021-02-24 20:05+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: en_US\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.4.2\n"
"X-Poedit-Basepath: ..\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-KeywordsList: __;_e;esc_html__;esc_attr;esc_attr_e;esc_html_e;"
"esc_attr__;_nx\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: node_modules\n"

#: comments.php:21
msgid "Post Comments"
msgstr ""

#: comments.php:28
msgid "comments"
msgstr ""

#: comments.php:36
msgid "Comment Navigation Above"
msgstr ""

#: comments.php:37 comments.php:55
msgid "Comment navigation"
msgstr ""

#: comments.php:38 comments.php:56
msgid "&larr; Older Comments"
msgstr ""

#: comments.php:39 comments.php:57
msgid "Newer Comments &rarr;"
msgstr ""

#: comments.php:54
msgid "Comment Navigation Below"
msgstr ""

#: comments.php:64
msgid "Comments are closed."
msgstr ""

#: content-404.php:13
msgid "That page can&rsquo;t be found."
msgstr ""

#: content-404.php:16
msgid ""
"Nothing was found at this location. Try searching, or check out the popular "
"items below."
msgstr ""

#: content-404.php:22 content-404.php:24
msgid "Popular Products"
msgstr ""

#: content-none.php:14
msgid "Nothing Found"
msgstr ""

#: content-none.php:20
#, php-format
msgid ""
"Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr ""

#: content-none.php:24
msgid ""
"Sorry, but nothing matched your search terms. Please try again with some "
"different keywords."
msgstr ""

#: content-none.php:29
msgid ""
"It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps "
"searching can help."
msgstr ""

#: functions.php:194
msgid "Elementor"
msgstr ""

#: functions.php:199 inc/setup/sections/intro.php:35
msgid "Kirki"
msgstr ""

#: functions.php:211
msgid "One Click Demo Import"
msgstr ""

#: functions.php:216 inc/customizer/fields/layout.php:829
#: inc/customizer/sections/color.php:43 inc/customizer/sections/layout.php:30
#: inc/customizer/sections/typography.php:122
#: inc/customizer/demir-customizer-extensions.php:185
msgid "WooCommerce"
msgstr ""

#: functions.php:234 inc/core/functions/class-tgm-plugin-activation.php:334
msgid "Install Required Plugins"
msgstr ""

#: functions.php:235 inc/core/functions/class-tgm-plugin-activation.php:335
msgid "Install Plugins"
msgstr ""

#: functions.php:236 inc/core/functions/class-tgm-plugin-activation.php:337
#, php-format
msgid "Installing Plugin: %s"
msgstr ""

#: functions.php:237 inc/core/functions/class-tgm-plugin-activation.php:340
msgid "Something went wrong with the plugin API."
msgstr ""

#: functions.php:248 inc/core/functions/class-tgm-plugin-activation.php:392
msgid "Return to Required Plugins Installer"
msgstr ""

#: functions.php:249 inc/core/functions/class-tgm-plugin-activation.php:394
#: inc/core/functions/class-tgm-plugin-activation.php:3263
msgid "Plugin activated successfully."
msgstr ""

#: functions.php:250
#, php-format
msgid "All plugins installed and activated successfully. %s"
msgstr ""

#: functions.php:263
msgid "demir Demo Data"
msgstr ""

#: functions.php:297
msgid "demir demo content imported!"
msgstr ""

#: functions.php:438
msgid "-preload"
msgstr ""

#: inc/class-demir.php:117
msgid "Primary Menu"
msgstr ""

#: inc/class-demir.php:118
msgid "Secondary Menu"
msgstr ""

#: inc/class-demir.php:213
msgid "Sidebar"
msgstr ""

#: inc/class-demir.php:219
msgid "Sidebar Posts"
msgstr ""

#: inc/class-demir.php:221
msgid "The posts sidebar."
msgstr ""

#: inc/class-demir.php:225
msgid "Sidebar Pages"
msgstr ""

#: inc/class-demir.php:227
msgid "The pages sidebar."
msgstr ""

#: inc/class-demir.php:231 inc/customizer/sections/color.php:25
msgid "Below Header"
msgstr ""

#: inc/class-demir.php:233
msgid ""
"Widgets added to this region will appear beneath the header and above the "
"main content."
msgstr ""

#: inc/class-demir.php:237
msgid "Top Bar Left"
msgstr ""

#: inc/class-demir.php:239
msgid ""
"A widget added to this region will appear at the very top of the site to the "
"left."
msgstr ""

#: inc/class-demir.php:245
msgid "Top Bar Center"
msgstr ""

#: inc/class-demir.php:247
msgid ""
"A widget added to this region will appear at the very top of the site in the "
"center."
msgstr ""

#: inc/class-demir.php:253
msgid "Top Bar Right"
msgstr ""

#: inc/class-demir.php:255
msgid ""
"A widget added to this region will appear at the very top of the site to the "
"right."
msgstr ""

#: inc/class-demir.php:261
msgid "Single Product Custom Area"
msgstr ""

#: inc/class-demir.php:263
msgid ""
"A widget added to this region will appear below the \"Add to cart\" button "
"on a product page."
msgstr ""

#: inc/class-demir.php:267
msgid "Floating Button Modal Content"
msgstr ""

#: inc/class-demir.php:269
msgid ""
"A widget added to this region will appear within a modal window on a single "
"product page. It is intended for a form shortcode, e.g. Contact Form 7 - but "
"you can add any content you wish."
msgstr ""

#: inc/class-demir.php:273
msgid "Below Cart Summary"
msgstr ""

#: inc/class-demir.php:275
msgid "A widget added to this region will appear below the cart summary."
msgstr ""

#: inc/class-demir.php:279
msgid "Cart Custom Area"
msgstr ""

#: inc/class-demir.php:281
msgid ""
"A widget added to this region will appear below the \"Proceed to checkout\" "
"button on the Cart page."
msgstr ""

#: inc/class-demir.php:285
msgid "Checkout Custom Area"
msgstr ""

#: inc/class-demir.php:287
msgid ""
"A widget added to this region will appear below the \"Place order\" button "
"on the Checkout page."
msgstr ""

#: inc/class-demir.php:291
msgid "Thank You Custom Area"
msgstr ""

#: inc/class-demir.php:293
msgid ""
"A widget added to this region will appear at the bottom of the thank you "
"page after an order has been placed."
msgstr ""

#: inc/class-demir.php:297
msgid "Below Content"
msgstr ""

#: inc/class-demir.php:299
msgid "A widget added to this region will appear below the main content area."
msgstr ""

#: inc/class-demir.php:303 inc/customizer/sections/color.php:49
#: inc/customizer/sections/layout.php:37
msgid "Footer"
msgstr ""

#: inc/class-demir.php:305
msgid "A widget added to this region will appear within the footer area."
msgstr ""

#: inc/class-demir.php:309
msgid "Copyright"
msgstr ""

#: inc/class-demir.php:311
msgid "A widget added to this region will appear within the copyright area."
msgstr ""

#: inc/class-demir.php:315
msgid "Mobile Extra"
msgstr ""

#: inc/class-demir.php:317
msgid ""
"A widget added to this region will appear below the mobile navigation area."
msgstr ""

#: inc/class-demir.php:537
msgid "Post Navigation"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:339
#, php-format
msgid "Updating Plugin: %s"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:393
#: inc/core/functions/class-tgm-plugin-activation.php:914
#: inc/core/functions/class-tgm-plugin-activation.php:2637
#: inc/core/functions/class-tgm-plugin-activation.php:3687
msgid "Return to the Dashboard"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:395
msgid "The following plugin was activated successfully:"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:397
#, php-format
msgid "No action taken. Plugin %1$s was already active."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:399
#, php-format
msgid ""
"Plugin not activated. A higher version of %s is needed for this theme. "
"Please update the plugin."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:401
#, php-format
msgid "All plugins installed and activated successfully. %1$s"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:402
msgid "Dismiss this notice"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:403
msgid ""
"There are one or more required or recommended plugins to install, update or "
"activate."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:404
msgid "Please contact the administrator of this site for help."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:607
msgid "This plugin needs to be updated to be compatible with your theme."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:608
msgid "Update Required"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:1022
msgid ""
"The remote plugin package does not contain a folder with the desired slug "
"and renaming did not work."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:1022
#: inc/core/functions/class-tgm-plugin-activation.php:1030
msgid ""
"Please contact the plugin provider and ask them to package their plugin "
"according to the WordPress guidelines."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:1030
msgid ""
"The remote plugin package consists of more than one file, but the files are "
"not packaged in a folder."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2086
#, php-format
msgid "TGMPA v%s"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2377
msgid "Required"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2380
msgid "Recommended"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2396
msgid "WordPress Repository"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2399
msgid "External Source"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2402
msgid "Pre-Packaged"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2419
msgid "Not Installed"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2423
msgid "Installed But Not Activated"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2425
msgid "Active"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2431
msgid "Required Update not Available"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2434
msgid "Requires Update"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2437
msgid "Update recommended"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2492
#, php-format
msgid "All <span class=\"count\">(%s)</span>"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2594
msgid "Installed version:"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2602
msgid "Minimum required version:"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2614
msgid "Available version:"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2637
msgid "No plugins to install, update or activate."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2651
msgid "Plugin"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2652
msgid "Source"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2653
msgid "Type"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2657
msgid "Version"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2658
msgid "Status"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2707
#, php-format
msgid "Install %2$s"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2712
#, php-format
msgid "Update %2$s"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2718
#, php-format
msgid "Activate %2$s"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2788
msgid "Upgrade message from the plugin author:"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2821
msgid "Install"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2827
msgid "Update"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2830
msgid "Activate"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2861
msgid "No plugins were selected to be installed. No action taken."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2863
msgid "No plugins were selected to be updated. No action taken."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2904
msgid "No plugins are available to be installed at this time."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:2906
msgid "No plugins are available to be updated at this time."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3012
msgid "No plugins were selected to be activated. No action taken."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3038
msgid "No plugins are available to be activated at this time."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3262
msgid "Plugin activation failed."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3604
#, php-format
msgid "Updating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3607
#, php-format
msgid "An error occurred while installing %1$s: <strong>%2$s</strong>."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3609
#, php-format
msgid "The installation of %1$s failed."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3613
msgid ""
"The installation and activation process is starting. This process may take a "
"while on some hosts, so please be patient."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3615
#, php-format
msgid "%1$s installed and activated successfully."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3616
msgid "All installations and activations have been completed."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3618
#, php-format
msgid "Installing and Activating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3621
msgid ""
"The installation process is starting. This process may take a while on some "
"hosts, so please be patient."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3623
#, php-format
msgid "%1$s installed."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3624
msgid "All installations have been completed."
msgstr ""

#: inc/core/functions/class-tgm-plugin-activation.php:3626
#, php-format
msgid "Installing Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/customizer/controls/custom-controls.php:185
msgid "Select weight & style"
msgstr ""

#: inc/customizer/fields/color.php:18
msgid "Primary swatch color"
msgstr ""

#: inc/customizer/fields/color.php:19
msgid "Select the primary color of your brand."
msgstr ""

#: inc/customizer/fields/color.php:100
msgid "General links"
msgstr ""

#: inc/customizer/fields/color.php:126
msgid "General links hover"
msgstr ""

#: inc/customizer/fields/color.php:152
msgid "Body background color"
msgstr ""

#: inc/customizer/fields/color.php:153
msgid "Visible if the grid is contained."
msgstr ""

#: inc/customizer/fields/color.php:179
msgid "Top bar background"
msgstr ""

#: inc/customizer/fields/color.php:205
msgid "Top Bar text color"
msgstr ""

#: inc/customizer/fields/color.php:231
msgid "Top bar border"
msgstr ""

#: inc/customizer/fields/color.php:260
msgid "Header background"
msgstr ""

#: inc/customizer/fields/color.php:298
msgid "Header border color"
msgstr ""

#: inc/customizer/fields/color.php:333
msgid "Mobile"
msgstr ""

#: inc/customizer/fields/color.php:343
msgid "Navigation icon"
msgstr ""

#: inc/customizer/fields/color.php:392 inc/customizer/fields/mainmenu.php:677
#: inc/customizer/fields/mainmenu.php:796
#: inc/customizer/fields/mainmenu.php:804
msgid "Cart icon"
msgstr ""

#: inc/customizer/fields/color.php:454
msgid "Mobile navigation background"
msgstr ""

#: inc/customizer/fields/color.php:483
msgid "Mobile navigation text"
msgstr ""

#: inc/customizer/fields/color.php:527
msgid "Mobile navigation divider"
msgstr ""

#: inc/customizer/fields/color.php:557
msgid "Navigation background"
msgstr ""

#: inc/customizer/fields/color.php:593
msgid "Below header background"
msgstr ""

#: inc/customizer/fields/color.php:619
msgid "Below header text color"
msgstr ""

#: inc/customizer/fields/color.php:648
msgid "Primary Button"
msgstr ""

#: inc/customizer/fields/color.php:658
msgid "Primary button text color"
msgstr ""

#: inc/customizer/fields/color.php:727
msgid "Primary button background"
msgstr ""

#: inc/customizer/fields/color.php:788
msgid "Primary button background hover"
msgstr ""

#: inc/customizer/fields/color.php:851
msgid "Sale Flash"
msgstr ""

#: inc/customizer/fields/color.php:861
msgid "Sale flash background"
msgstr ""

#: inc/customizer/fields/color.php:904
msgid "Sale flash text color"
msgstr ""

#: inc/customizer/fields/color.php:931
msgid " Ratings"
msgstr ""

#: inc/customizer/fields/color.php:941
msgid "Star ratings color"
msgstr ""

#: inc/customizer/fields/color.php:984
msgid " Product Archives"
msgstr ""

#: inc/customizer/fields/color.php:994
msgid "Archive description background"
msgstr ""

#: inc/customizer/fields/color.php:1020
msgid "Archive description text"
msgstr ""

#: inc/customizer/fields/color.php:1047
msgid " Single Product"
msgstr ""

#: inc/customizer/fields/color.php:1057
msgid "Product container background"
msgstr ""

#: inc/customizer/fields/color.php:1083
msgid "Floating button background"
msgstr ""

#: inc/customizer/fields/color.php:1110
msgid "Floating button text color"
msgstr ""

#: inc/customizer/fields/color.php:1137
msgid " Cart and Checkout"
msgstr ""

#: inc/customizer/fields/color.php:1148
msgid "Progress bar color"
msgstr ""

#: inc/customizer/fields/color.php:1174
msgid "Below content icons"
msgstr ""

#: inc/customizer/fields/color.php:1208
msgid "Footer background"
msgstr ""

#: inc/customizer/fields/color.php:1235
msgid "Footer headings color"
msgstr ""

#: inc/customizer/fields/color.php:1261
msgid "Footer text color"
msgstr ""

#: inc/customizer/fields/color.php:1288
msgid "Footer links"
msgstr ""

#: inc/customizer/fields/color.php:1315
msgid "Footer links hover"
msgstr ""

#: inc/customizer/fields/general.php:18 inc/customizer/fields/general.php:50
msgid "Logo height"
msgstr ""

#: inc/customizer/fields/general.php:19 inc/customizer/fields/general.php:51
msgid ""
"Adjust the height of your logo in pixels. You can upload your logo image "
"within the \"Site Identity\" panel."
msgstr ""

#: inc/customizer/fields/general.php:82
msgid "Display tagline under logo"
msgstr ""

#: inc/customizer/fields/general.php:83
msgid "This is set within Settings > General"
msgstr ""

#: inc/customizer/fields/general.php:97
msgid "Sticky logo"
msgstr ""

#: inc/customizer/fields/general.php:117
msgid "Sticky logo width"
msgstr ""

#: inc/customizer/fields/general.php:118
msgid "Suggested width of at least 60"
msgstr ""

#: inc/customizer/fields/general.php:154
msgid "Mobile header height"
msgstr ""

#: inc/customizer/fields/general.php:155
msgid "Adjust height of your mobile header (px)"
msgstr ""

#: inc/customizer/fields/general.php:188
msgid "Mobile logo height"
msgstr ""

#: inc/customizer/fields/general.php:189
msgid "Adjust height of your mobile logo (px)"
msgstr ""

#: inc/customizer/fields/general.php:215
msgid "Mobile sticky header"
msgstr ""

#: inc/customizer/fields/general.php:219 inc/customizer/fields/general.php:236
#: inc/customizer/fields/general.php:276 inc/customizer/fields/mainmenu.php:25
#: inc/customizer/fields/mainmenu.php:322
#: inc/customizer/fields/mainmenu.php:337
msgid "Enable"
msgstr ""

#: inc/customizer/fields/general.php:220 inc/customizer/fields/general.php:237
#: inc/customizer/fields/general.php:277 inc/customizer/fields/mainmenu.php:26
#: inc/customizer/fields/mainmenu.php:224
#: inc/customizer/fields/mainmenu.php:323
#: inc/customizer/fields/mainmenu.php:338
msgid "Disable"
msgstr ""

#: inc/customizer/fields/general.php:232
msgid "Show search on mobile"
msgstr ""

#: inc/customizer/fields/general.php:249
msgid "Mobile search position"
msgstr ""

#: inc/customizer/fields/general.php:259
msgid "Within navigation"
msgstr ""

#: inc/customizer/fields/general.php:260
msgid "Below header bar"
msgstr ""

#: inc/customizer/fields/general.php:261
msgid "Header icon and toggle"
msgstr ""

#: inc/customizer/fields/general.php:272
msgid "Show my account on mobile"
msgstr ""

#: inc/customizer/fields/general.php:289
msgid "Display mobile menu label"
msgstr ""

#: inc/customizer/fields/general.php:293 inc/customizer/fields/general.php:348
#: inc/customizer/fields/general.php:378 inc/customizer/fields/general.php:406
#: inc/customizer/fields/layout.php:32
msgid "Yes"
msgstr ""

#: inc/customizer/fields/general.php:294 inc/customizer/fields/general.php:349
#: inc/customizer/fields/general.php:379 inc/customizer/fields/general.php:407
#: inc/customizer/fields/layout.php:33
msgid "No"
msgstr ""

#: inc/customizer/fields/general.php:306
msgid "Mobile menu label text"
msgstr ""

#: inc/customizer/fields/general.php:334
msgid "Critical CSS"
msgstr ""

#: inc/customizer/fields/general.php:344
msgid "Enable critical CSS?"
msgstr ""

#: inc/customizer/fields/general.php:364
msgid "Minification Settings"
msgstr ""

#: inc/customizer/fields/general.php:374
msgid "Load minified CSS files?"
msgstr ""

#: inc/customizer/fields/general.php:392
msgid "Icon Font"
msgstr ""

#: inc/customizer/fields/general.php:402
msgid "Load Rivolicons icon font?"
msgstr ""

#: inc/customizer/fields/layout.php:19
msgid "Wrapper"
msgstr ""

#: inc/customizer/fields/layout.php:28
msgid "Contain the grid?"
msgstr ""

#: inc/customizer/fields/layout.php:45
msgid "Wraper container width"
msgstr ""

#: inc/customizer/fields/layout.php:46
msgid "Adjust wrapper width in px."
msgstr ""

#: inc/customizer/fields/layout.php:78
msgid "Content container"
msgstr ""

#: inc/customizer/fields/layout.php:88
msgid "Content container width"
msgstr ""

#: inc/customizer/fields/layout.php:89
msgid ""
"Adjust the width of your content container in pixels. Default is 1170px."
msgstr ""

#: inc/customizer/fields/layout.php:137
msgid "Breadcrumbs"
msgstr ""

#: inc/customizer/fields/layout.php:147
msgid "Display breadcrumbs"
msgstr ""

#: inc/customizer/fields/layout.php:160
msgid "Breadcrumbs type"
msgstr ""

#: inc/customizer/fields/layout.php:171 inc/customizer/fields/layout.php:320
#: inc/customizer/js/wp-color-picker-alpha.js:48
msgid "Default"
msgstr ""

#: inc/customizer/fields/layout.php:172
msgid "Rank Math Breadcrumbs"
msgstr ""

#: inc/customizer/fields/layout.php:173
msgid "SEOPress Breadcrumbs"
msgstr ""

#: inc/customizer/fields/layout.php:174
msgid "Yoast Breadcrumbs"
msgstr ""

#: inc/customizer/fields/layout.php:187 inc/customizer/panels/panels.php:13
#: inc/customizer/sections/color.php:37 inc/customizer/sections/layout.php:12
msgid "General"
msgstr ""

#: inc/customizer/fields/layout.php:197
msgid "Enable sidebar cart drawer"
msgstr ""

#: inc/customizer/fields/layout.php:210
msgid "Enable single product ajax"
msgstr ""

#: inc/customizer/fields/layout.php:211
msgid "Add directly to the cart on single products"
msgstr ""

#: inc/customizer/fields/layout.php:226
msgid "Shop"
msgstr ""

#: inc/customizer/fields/layout.php:237
msgid "Display product results count"
msgstr ""

#: inc/customizer/fields/layout.php:250
msgid "Display product sorting"
msgstr ""

#: inc/customizer/fields/layout.php:263
#, php-format
msgid "Display sale % discount"
msgstr ""

#: inc/customizer/fields/layout.php:275
msgid "Display rating"
msgstr ""

#: inc/customizer/fields/layout.php:288
msgid "Display category"
msgstr ""

#: inc/customizer/fields/layout.php:301
msgid "Image change on hover"
msgstr ""

#: inc/customizer/fields/layout.php:314
msgid "Product card display"
msgstr ""

#: inc/customizer/fields/layout.php:321
msgid "Slide up"
msgstr ""

#: inc/customizer/fields/layout.php:331
msgid "Button display"
msgstr ""

#: inc/customizer/fields/layout.php:337
msgid "On hover (desktop only)"
msgstr ""

#: inc/customizer/fields/layout.php:338
msgid "Static"
msgstr ""

#: inc/customizer/fields/layout.php:339
msgid "Remove buttons"
msgstr ""

#: inc/customizer/fields/layout.php:349
msgid "Product text alignment"
msgstr ""

#: inc/customizer/fields/layout.php:355 inc/customizer/fields/layout.php:846
#: inc/customizer/fields/layout.php:884 inc/customizer/fields/layout.php:920
#: inc/customizer/fields/layout.php:957
msgid "Left"
msgstr ""

#: inc/customizer/fields/layout.php:356
msgid "Center"
msgstr ""

#: inc/customizer/fields/layout.php:357 inc/customizer/fields/layout.php:847
#: inc/customizer/fields/layout.php:885 inc/customizer/fields/layout.php:921
#: inc/customizer/fields/layout.php:958
msgid "Right"
msgstr ""

#: inc/customizer/fields/layout.php:367
msgid "Use masonry layout"
msgstr ""

#: inc/customizer/fields/layout.php:368
msgid "Only enable if you have irregular image sizes."
msgstr ""

#: inc/customizer/fields/layout.php:382
msgid "Product Categories"
msgstr ""

#: inc/customizer/fields/layout.php:392
msgid "Category description layout."
msgstr ""

#: inc/customizer/fields/layout.php:398
msgid "Below header"
msgstr ""

#: inc/customizer/fields/layout.php:399
msgid "Within content"
msgstr ""

#: inc/customizer/fields/layout.php:409
msgid "Display category description"
msgstr ""

#: inc/customizer/fields/layout.php:429
msgid "Display category image"
msgstr ""

#: inc/customizer/fields/layout.php:450
msgid "Single Product"
msgstr ""

#: inc/customizer/fields/layout.php:461
msgid "Product gallery thumbnails"
msgstr ""

#: inc/customizer/fields/layout.php:467
msgid "Horizontal"
msgstr ""

#: inc/customizer/fields/layout.php:468
msgid "Vertical"
msgstr ""

#: inc/customizer/fields/layout.php:490
msgid "Display sticky add to cart bar"
msgstr ""

#: inc/customizer/fields/layout.php:503
msgid "Sticky add to cart bar position"
msgstr ""

#: inc/customizer/fields/layout.php:514 inc/customizer/fields/layout.php:809
msgid "Top"
msgstr ""

#: inc/customizer/fields/layout.php:515 inc/customizer/fields/layout.php:810
msgid "Bottom"
msgstr ""

#: inc/customizer/fields/layout.php:538
msgid "Display product meta data"
msgstr ""

#: inc/customizer/fields/layout.php:551
msgid "Display previous/next"
msgstr ""

#: inc/customizer/fields/layout.php:576
msgid "Display floating button"
msgstr ""

#: inc/customizer/fields/layout.php:589
msgid "Floating button title:"
msgstr ""

#: inc/customizer/fields/layout.php:590
msgid "Content is added within the widget: \"Floating Button Modal Content\""
msgstr ""

#: inc/customizer/fields/layout.php:626
msgid "Display related"
msgstr ""

#: inc/customizer/fields/layout.php:639
msgid "Number of related items to show"
msgstr ""

#: inc/customizer/fields/layout.php:665
msgid "Display upsells before related"
msgstr ""

#: inc/customizer/fields/layout.php:678
msgid "Upsells title"
msgstr ""

#: inc/customizer/fields/layout.php:697
msgid "Number of upsells to show"
msgstr ""

#: inc/customizer/fields/layout.php:713
msgid "Cart and Checkout"
msgstr ""

#: inc/customizer/fields/layout.php:723
msgid "Display progress bar"
msgstr ""

#: inc/customizer/fields/layout.php:735
msgid "Cross sells position"
msgstr ""

#: inc/customizer/fields/layout.php:741
msgid "After cart table"
msgstr ""

#: inc/customizer/fields/layout.php:742
msgid "Bottom of page"
msgstr ""

#: inc/customizer/fields/layout.php:752
msgid "Number of cross sells to show"
msgstr ""

#: inc/customizer/fields/layout.php:767
msgid "Mobile cart page (experimental)"
msgstr ""

#: inc/customizer/fields/layout.php:768
msgid "Collapses the cart page table on mobile"
msgstr ""

#: inc/customizer/fields/layout.php:790
msgid "Distraction-free checkout"
msgstr ""

#: inc/customizer/fields/layout.php:791
msgid "Simplifies the checkout experience for better conversions."
msgstr ""

#: inc/customizer/fields/layout.php:803
msgid "Checkout coupon code position"
msgstr ""

#: inc/customizer/fields/layout.php:840
msgid "WooCommerce Sidebar"
msgstr ""

#: inc/customizer/fields/layout.php:848 inc/customizer/fields/layout.php:922
#: inc/customizer/fields/layout.php:959
msgid "None"
msgstr ""

#: inc/customizer/fields/layout.php:868
msgid "Pages"
msgstr ""

#: inc/customizer/fields/layout.php:878
msgid "Page Sidebar"
msgstr ""

#: inc/customizer/fields/layout.php:904
msgid "Blog Archives"
msgstr ""

#: inc/customizer/fields/layout.php:914
msgid "Blog Archives Sidebar"
msgstr ""

#: inc/customizer/fields/layout.php:941
msgid "Single Post"
msgstr ""

#: inc/customizer/fields/layout.php:951
msgid "Post Sidebar"
msgstr ""

#: inc/customizer/fields/layout.php:969
msgid "Sidebar Width (%)."
msgstr ""

#: inc/customizer/fields/layout.php:970
msgid "Adjust the width of the sidebar."
msgstr ""

#: inc/customizer/fields/layout.php:994
msgid "Content Width (%)."
msgstr ""

#: inc/customizer/fields/layout.php:995
msgid "Adjust the width of the content."
msgstr ""

#: inc/customizer/fields/layout.php:1020
msgid "Blog Layout"
msgstr ""

#: inc/customizer/fields/layout.php:1026
msgid "List"
msgstr ""

#: inc/customizer/fields/layout.php:1027
msgid "Flow"
msgstr ""

#: inc/customizer/fields/layout.php:1028
msgid "Grid of 2"
msgstr ""

#: inc/customizer/fields/layout.php:1029
msgid "Grid of 3"
msgstr ""

#: inc/customizer/fields/layout.php:1039
msgid "Display blog post summary"
msgstr ""

#: inc/customizer/fields/layout.php:1061
msgid "Single Posts"
msgstr ""

#: inc/customizer/fields/layout.php:1071
msgid "Display blog author"
msgstr ""

#: inc/customizer/fields/layout.php:1084
msgid "Display blog meta"
msgstr ""

#: inc/customizer/fields/layout.php:1097
msgid "Display blog previous/next"
msgstr ""

#: inc/customizer/fields/layout.php:1109
msgid "Display featured image"
msgstr ""

#: inc/customizer/fields/layout.php:1123
msgid "Single Post Layout."
msgstr ""

#: inc/customizer/fields/layout.php:1124
msgid ""
"Layout Two is full width and better for using with the Gutenberg editor."
msgstr ""

#: inc/customizer/fields/layout.php:1130
msgid "Layout One"
msgstr ""

#: inc/customizer/fields/layout.php:1131
msgid "Layout Two"
msgstr ""

#: inc/customizer/fields/layout.php:1143
msgid "Show Below Content?"
msgstr ""

#: inc/customizer/fields/layout.php:1149 inc/customizer/fields/layout.php:1166
#: inc/customizer/fields/layout.php:1183 inc/customizer/fields/mainmenu.php:49
msgid "Show"
msgstr ""

#: inc/customizer/fields/layout.php:1150 inc/customizer/fields/layout.php:1167
#: inc/customizer/fields/layout.php:1184 inc/customizer/fields/mainmenu.php:50
msgid "Hide"
msgstr ""

#: inc/customizer/fields/layout.php:1160
msgid "Show Footer?"
msgstr ""

#: inc/customizer/fields/layout.php:1177
msgid "Show Copyright?"
msgstr ""

#: inc/customizer/fields/mainmenu.php:18
msgid "Display top bar?"
msgstr ""

#: inc/customizer/fields/mainmenu.php:19
msgid "Enable or disable the top bar"
msgstr ""

#: inc/customizer/fields/mainmenu.php:36
msgid "Hide top bar on mobile?"
msgstr ""

#: inc/customizer/fields/mainmenu.php:61
msgid "Header Layout"
msgstr ""

#: inc/customizer/fields/mainmenu.php:62
msgid "Change the header layout"
msgstr ""

#: inc/customizer/fields/mainmenu.php:68
msgid "Logo / Search / Secondary"
msgstr ""

#: inc/customizer/fields/mainmenu.php:69
msgid "Logo / Search / Secondary / Cart"
msgstr ""

#: inc/customizer/fields/mainmenu.php:70
msgid "Search / Logo / Secondary"
msgstr ""

#: inc/customizer/fields/mainmenu.php:71
msgid "Secondary / Logo / Search"
msgstr ""

#: inc/customizer/fields/mainmenu.php:72
msgid "Logo / Navigation / Cart"
msgstr ""

#: inc/customizer/fields/mainmenu.php:82
msgid "Header Container"
msgstr ""

#: inc/customizer/fields/mainmenu.php:83
msgid "Change the header container"
msgstr ""

#: inc/customizer/fields/mainmenu.php:96
msgid "Contained"
msgstr ""

#: inc/customizer/fields/mainmenu.php:97
msgid "Full width"
msgstr ""

#: inc/customizer/fields/mainmenu.php:108
msgid "Header Top Padding"
msgstr ""

#: inc/customizer/fields/mainmenu.php:109
msgid "Adjust the header top padding"
msgstr ""

#: inc/customizer/fields/mainmenu.php:142
msgid "Header Bottom Padding"
msgstr ""

#: inc/customizer/fields/mainmenu.php:143
msgid "Adjust the header bottom padding"
msgstr ""

#: inc/customizer/fields/mainmenu.php:175
msgid "Header Height"
msgstr ""

#: inc/customizer/fields/mainmenu.php:176
msgid "Adjust the header height"
msgstr ""

#: inc/customizer/fields/mainmenu.php:214
msgid "Display the search?"
msgstr ""

#: inc/customizer/fields/mainmenu.php:215
msgid "Enable or disable the search"
msgstr ""

#: inc/customizer/fields/mainmenu.php:221
msgid "Product Search"
msgstr ""

#: inc/customizer/fields/mainmenu.php:222
msgid "Ajax Search for WooCommerce Plugin"
msgstr ""

#: inc/customizer/fields/mainmenu.php:223
msgid "Regular Search"
msgstr ""

#: inc/customizer/fields/mainmenu.php:234
msgid "Search modal title"
msgstr ""

#: inc/customizer/fields/mainmenu.php:235
msgid "Default: Recently added"
msgstr ""

#: inc/customizer/fields/mainmenu.php:262
msgid "Navigation Height"
msgstr ""

#: inc/customizer/fields/mainmenu.php:263
msgid "Adjust the navigation height"
msgstr ""

#: inc/customizer/fields/mainmenu.php:301
msgid "Display menu descriptions"
msgstr ""

#: inc/customizer/fields/mainmenu.php:315
msgid "Sticky Navigation"
msgstr ""

#: inc/customizer/fields/mainmenu.php:316
msgid "Stick the navigation on scroll"
msgstr ""

#: inc/customizer/fields/mainmenu.php:333
msgid "Mobile Sticky Header"
msgstr ""

#: inc/customizer/fields/mainmenu.php:351
#: inc/customizer/fields/mainmenu.php:396
msgid "Navigation links"
msgstr ""

#: inc/customizer/fields/mainmenu.php:441
msgid "Navigation links hover/selected"
msgstr ""

#: inc/customizer/fields/mainmenu.php:466
msgid "Fade out other links on hover"
msgstr ""

#: inc/customizer/fields/mainmenu.php:467
msgid "Opacity (%)."
msgstr ""

#: inc/customizer/fields/mainmenu.php:508
msgid "Dropdowns"
msgstr ""

#: inc/customizer/fields/mainmenu.php:519
msgid "Navigation dropdown background"
msgstr ""

#: inc/customizer/fields/mainmenu.php:547
msgid "Navigation dropdown text"
msgstr ""

#: inc/customizer/fields/mainmenu.php:575
msgid "Navigation dropdown hover"
msgstr ""

#: inc/customizer/fields/mainmenu.php:613
#: inc/demir-template-functions.php:589
msgid "Secondary Navigation"
msgstr ""

#: inc/customizer/fields/mainmenu.php:624
msgid "Secondary navigation color"
msgstr ""

#: inc/customizer/fields/mainmenu.php:667
#: inc/customizer/sections/mainmenu.php:43
msgid "Cart"
msgstr ""

#: inc/customizer/fields/mainmenu.php:729
msgid "Cart text"
msgstr ""

#: inc/customizer/fields/mainmenu.php:755
msgid "Cart text hover color"
msgstr ""

#: inc/customizer/fields/mainmenu.php:783
msgid "Display cart"
msgstr ""

#: inc/customizer/fields/mainmenu.php:797
msgid ""
"After switching, test in an incognito window. The previous selection will be "
"likely cached as a fragment."
msgstr ""

#: inc/customizer/fields/mainmenu.php:803
msgid "Basket (Default)"
msgstr ""

#: inc/customizer/fields/mainmenu.php:805
msgid "Bag icon"
msgstr ""

#: inc/customizer/fields/mainmenu.php:815
msgid "Cart sidebar title"
msgstr ""

#: inc/customizer/fields/mainmenu.php:834
msgid "Cart sidebar below text"
msgstr ""

#: inc/customizer/fields/typography.php:15
msgid "Select a Typography Preset"
msgstr ""

#: inc/customizer/fields/typography.php:16
msgid "Change your font style with one click."
msgstr ""

#: inc/customizer/fields/typography.php:272
#: inc/customizer/demir-customizer-extensions.php:213
msgid "Font settings"
msgstr ""

#: inc/customizer/fields/typography.php:303
msgid "Primary Navigation Font"
msgstr ""

#: inc/customizer/fields/typography.php:328
msgid "Navigation Dropdown Font"
msgstr ""

#: inc/customizer/fields/typography.php:352
msgid "Mega Menu Heading Font"
msgstr ""

#: inc/customizer/fields/typography.php:378
#: inc/customizer/fields/typography.php:411
#: inc/customizer/fields/typography.php:443
#: inc/customizer/fields/typography.php:477
#: inc/customizer/fields/typography.php:511
#: inc/customizer/fields/typography.php:545
#: inc/customizer/fields/typography.php:573
#: inc/customizer/fields/typography.php:606
msgid "Font Settings"
msgstr ""

#: inc/customizer/fields/typography.php:634
msgid "Blog Post Title"
msgstr ""

#: inc/customizer/fields/typography.php:662
msgid "Archives Category Description"
msgstr ""

#: inc/customizer/fields/typography.php:689
msgid "Archives Product Title"
msgstr ""

#: inc/customizer/fields/typography.php:719
msgid "Single Product Title"
msgstr ""

#: inc/customizer/fields/typography.php:747
msgid "Primary Buttons"
msgstr ""

#: inc/customizer/js/wp-color-picker-alpha.js:46
msgid "Color value"
msgstr ""

#: inc/customizer/js/wp-color-picker-alpha.js:47
msgid "Select Color"
msgstr ""

#: inc/customizer/js/wp-color-picker-alpha.js:49
msgid "Select default color"
msgstr ""

#: inc/customizer/js/wp-color-picker-alpha.js:50
msgid "Clear"
msgstr ""

#: inc/customizer/js/wp-color-picker-alpha.js:51
msgid "Clear color"
msgstr ""

#: inc/customizer/panels/panels.php:14
msgid "Manage general theme settings"
msgstr ""

#: inc/customizer/panels/panels.php:18
msgid "Colors"
msgstr ""

#: inc/customizer/panels/panels.php:19
msgid "Manage theme colors"
msgstr ""

#: inc/customizer/panels/panels.php:23
msgid "Header and Navigation"
msgstr ""

#: inc/customizer/panels/panels.php:24
msgid "Manage the header and navigation"
msgstr ""

#: inc/customizer/panels/panels.php:28
msgid "Page Heading"
msgstr ""

#: inc/customizer/panels/panels.php:29
msgid "Manage the page heading"
msgstr ""

#: inc/customizer/panels/panels.php:33
msgid "Typography"
msgstr ""

#: inc/customizer/panels/panels.php:34
#: inc/customizer/demir-customizer-extensions.php:50
msgid "Manage theme typography"
msgstr ""

#: inc/customizer/panels/panels.php:38
msgid "Layout"
msgstr ""

#: inc/customizer/panels/panels.php:39
msgid "Manage theme header, footer and more"
msgstr ""

#: inc/customizer/panels/panels.php:43 inc/customizer/sections/layout.php:24
#: inc/customizer/sections/typography.php:113
#: inc/customizer/demir-customizer-extensions.php:175
msgid "Blog"
msgstr ""

#: inc/customizer/panels/panels.php:44
msgid "Manage blog settings"
msgstr ""

#: inc/customizer/sections/color.php:13 inc/customizer/sections/mainmenu.php:13
msgid "Top Bar"
msgstr ""

#: inc/customizer/sections/color.php:19 inc/customizer/sections/mainmenu.php:20
msgid "Header"
msgstr ""

#: inc/customizer/sections/color.php:31 inc/customizer/sections/mainmenu.php:36
#: inc/customizer/sections/typography.php:32
#: inc/customizer/demir-customizer-extensions.php:85
msgid "Navigation"
msgstr ""

#: inc/customizer/sections/general.php:13
msgid "Site Logo"
msgstr ""

#: inc/customizer/sections/general.php:21
msgid "Sticky Logo"
msgstr ""

#: inc/customizer/sections/general.php:29
msgid "Speed Settings"
msgstr ""

#: inc/customizer/sections/layout.php:18
msgid "Sidebars"
msgstr ""

#: inc/customizer/sections/layout.php:31
msgid "Publish and refresh to see the changes."
msgstr ""

#: inc/customizer/sections/mainmenu.php:28
msgid "Mobile Header"
msgstr ""

#: inc/customizer/sections/mainmenu.php:50
msgid "Responsive Breakpoint"
msgstr ""

#: inc/customizer/sections/typography.php:14
#: inc/customizer/demir-customizer-extensions.php:65
msgid "Presets"
msgstr ""

#: inc/customizer/sections/typography.php:23
#: inc/customizer/demir-customizer-extensions.php:75
msgid "Main Body"
msgstr ""

#: inc/customizer/sections/typography.php:41
#: inc/customizer/demir-customizer-extensions.php:95
msgid "Paragraphs"
msgstr ""

#: inc/customizer/sections/typography.php:50
#: inc/customizer/demir-customizer-extensions.php:105
msgid "Heading One"
msgstr ""

#: inc/customizer/sections/typography.php:59
#: inc/customizer/demir-customizer-extensions.php:115
msgid "Heading Two"
msgstr ""

#: inc/customizer/sections/typography.php:68
#: inc/customizer/demir-customizer-extensions.php:125
msgid "Heading Three"
msgstr ""

#: inc/customizer/sections/typography.php:77
#: inc/customizer/demir-customizer-extensions.php:135
msgid "Heading Four"
msgstr ""

#: inc/customizer/sections/typography.php:86
#: inc/customizer/demir-customizer-extensions.php:145
msgid "Heading Five"
msgstr ""

#: inc/customizer/sections/typography.php:95
#: inc/customizer/demir-customizer-extensions.php:155
msgid "Blockquotes"
msgstr ""

#: inc/customizer/sections/typography.php:104
#: inc/customizer/demir-customizer-extensions.php:165
msgid "Widget Titles"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:49
msgid "Typography 2.0"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:214
#: inc/customizer/demir-customizer-extensions.php:306
#: inc/customizer/demir-customizer-extensions.php:405
#: inc/customizer/demir-customizer-extensions.php:480
#: inc/customizer/demir-customizer-extensions.php:598
#: inc/customizer/demir-customizer-extensions.php:716
#: inc/customizer/demir-customizer-extensions.php:858
#: inc/customizer/demir-customizer-extensions.php:1000
#: inc/customizer/demir-customizer-extensions.php:1142
#: inc/customizer/demir-customizer-extensions.php:1284
#: inc/customizer/demir-customizer-extensions.php:1426
#: inc/customizer/demir-customizer-extensions.php:1568
#: inc/customizer/demir-customizer-extensions.php:1710
#: inc/customizer/demir-customizer-extensions.php:1853
#: inc/customizer/demir-customizer-extensions.php:1995
#: inc/customizer/demir-customizer-extensions.php:2137
#: inc/customizer/demir-customizer-extensions.php:2279
msgid "All Google Fonts sorted alphabetically"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:237
#: inc/customizer/demir-customizer-extensions.php:329
#: inc/customizer/demir-customizer-extensions.php:428
#: inc/customizer/demir-customizer-extensions.php:503
#: inc/customizer/demir-customizer-extensions.php:621
#: inc/customizer/demir-customizer-extensions.php:739
#: inc/customizer/demir-customizer-extensions.php:881
#: inc/customizer/demir-customizer-extensions.php:1023
#: inc/customizer/demir-customizer-extensions.php:1165
#: inc/customizer/demir-customizer-extensions.php:1307
#: inc/customizer/demir-customizer-extensions.php:1449
#: inc/customizer/demir-customizer-extensions.php:1591
#: inc/customizer/demir-customizer-extensions.php:1733
#: inc/customizer/demir-customizer-extensions.php:1876
#: inc/customizer/demir-customizer-extensions.php:2018
#: inc/customizer/demir-customizer-extensions.php:2160
#: inc/customizer/demir-customizer-extensions.php:2302
msgid "Font size (px)"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:261
#: inc/customizer/demir-customizer-extensions.php:353
#: inc/customizer/demir-customizer-extensions.php:527
#: inc/customizer/demir-customizer-extensions.php:645
#: inc/customizer/demir-customizer-extensions.php:763
#: inc/customizer/demir-customizer-extensions.php:905
#: inc/customizer/demir-customizer-extensions.php:1047
#: inc/customizer/demir-customizer-extensions.php:1189
#: inc/customizer/demir-customizer-extensions.php:1331
#: inc/customizer/demir-customizer-extensions.php:1473
#: inc/customizer/demir-customizer-extensions.php:1615
#: inc/customizer/demir-customizer-extensions.php:1757
#: inc/customizer/demir-customizer-extensions.php:1900
#: inc/customizer/demir-customizer-extensions.php:2042
#: inc/customizer/demir-customizer-extensions.php:2184
#: inc/customizer/demir-customizer-extensions.php:2326
msgid "Letter spacing (px)"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:283
#: inc/customizer/demir-customizer-extensions.php:575
#: inc/customizer/demir-customizer-extensions.php:693
#: inc/customizer/demir-customizer-extensions.php:835
#: inc/customizer/demir-customizer-extensions.php:977
#: inc/customizer/demir-customizer-extensions.php:1119
#: inc/customizer/demir-customizer-extensions.php:1261
#: inc/customizer/demir-customizer-extensions.php:1403
#: inc/customizer/demir-customizer-extensions.php:1545
#: inc/customizer/demir-customizer-extensions.php:1687
#: inc/customizer/demir-customizer-extensions.php:1829
#: inc/customizer/demir-customizer-extensions.php:1972
#: inc/customizer/demir-customizer-extensions.php:2114
#: inc/customizer/demir-customizer-extensions.php:2256
#: inc/customizer/demir-customizer-extensions.php:2374
msgid "Font color"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:305
msgid "Primary navigation settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:377
#: inc/customizer/demir-customizer-extensions.php:452
#: inc/customizer/demir-customizer-extensions.php:551
#: inc/customizer/demir-customizer-extensions.php:669
#: inc/customizer/demir-customizer-extensions.php:787
#: inc/customizer/demir-customizer-extensions.php:929
#: inc/customizer/demir-customizer-extensions.php:1071
#: inc/customizer/demir-customizer-extensions.php:1213
#: inc/customizer/demir-customizer-extensions.php:1355
#: inc/customizer/demir-customizer-extensions.php:1497
#: inc/customizer/demir-customizer-extensions.php:1639
#: inc/customizer/demir-customizer-extensions.php:1781
#: inc/customizer/demir-customizer-extensions.php:1924
#: inc/customizer/demir-customizer-extensions.php:2066
#: inc/customizer/demir-customizer-extensions.php:2208
#: inc/customizer/demir-customizer-extensions.php:2350
msgid "Text transform"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:404
msgid "Navigation dropdown settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:479
msgid "Mega menu heading font settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:597
msgid "Paragraph font settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:715
msgid "H1 font settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:813
#: inc/customizer/demir-customizer-extensions.php:955
#: inc/customizer/demir-customizer-extensions.php:1097
#: inc/customizer/demir-customizer-extensions.php:1239
#: inc/customizer/demir-customizer-extensions.php:1381
#: inc/customizer/demir-customizer-extensions.php:1523
#: inc/customizer/demir-customizer-extensions.php:1665
#: inc/customizer/demir-customizer-extensions.php:1807
#: inc/customizer/demir-customizer-extensions.php:1950
#: inc/customizer/demir-customizer-extensions.php:2092
#: inc/customizer/demir-customizer-extensions.php:2234
msgid "Line height"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:857
msgid "H2 font settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:999
msgid "H3 font settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:1141
msgid "H4 font settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:1283
#: inc/customizer/demir-customizer-extensions.php:1425
msgid "H5 font settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:1567
msgid "Widget title font settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:1709
msgid "Blog post font settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:1852
msgid "WooCommerce Archives description font settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:1994
msgid "WooCommerce product listings font settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:2136
msgid "WooCommerce single product page font settings"
msgstr ""

#: inc/customizer/demir-customizer-extensions.php:2278
msgid "WooCommerce primary button font settings"
msgstr ""

#: inc/setup/help.php:44
#, php-format
msgid ""
"Thanks for choosing demir! You can read hints and tips on how get the "
"most out of your new theme in the %1$sHelp section%2$s."
msgstr ""

#: inc/setup/help.php:45
msgid "Get started with demir"
msgstr ""

#: inc/setup/help.php:65 inc/setup/help.php:77
msgid "demir Help"
msgstr ""

#: inc/setup/help.php:78
msgid "Everything you need to know to get the most out of demir."
msgstr ""

#: inc/setup/help.php:80
msgid "Getting Started"
msgstr ""

#: inc/setup/help.php:81 inc/setup/sections/usefulplugins.php:16
msgid "Useful Plugins"
msgstr ""

#: inc/setup/sections/intro.php:18
msgid "Welcome to demir!"
msgstr ""

#: inc/setup/sections/intro.php:24
msgid "demir Theme Documentation"
msgstr ""

#: inc/setup/sections/intro.php:25
msgid ""
"We provide lots of theme documentation articles including a detailed "
"installation and setup guide on our website. We have over 30 detailed "
"articles which explain the most common queries with screenshots."
msgstr ""

#: inc/setup/sections/intro.php:26
msgid "View demir Documentation"
msgstr ""

#: inc/setup/sections/intro.php:30
msgid "Theme Options"
msgstr ""

#: inc/setup/sections/intro.php:31
msgid ""
"The demir Theme Customizer enables you to customize many elements of "
"the theme directly without any coding skills. This includes options such as "
"uploading your logo, changing the primary color, and much more."
msgstr ""

#: inc/setup/sections/intro.php:33
msgid "To access the Customizer, go to"
msgstr ""

#: inc/setup/sections/intro.php:33
msgid "Appearance → Customize"
msgstr ""

#: inc/setup/sections/intro.php:33
msgid "in the WordPress admin menu."
msgstr ""

#: inc/setup/sections/intro.php:34
msgid "When you are finished making changes, click"
msgstr ""

#: inc/setup/sections/intro.php:34
msgid "Save & Publish"
msgstr ""

#: inc/setup/sections/intro.php:34
msgid "to save the settings. Check out your site to confirm your changes."
msgstr ""

#: inc/setup/sections/intro.php:35
msgid "You will need to have the"
msgstr ""

#: inc/setup/sections/intro.php:35
msgid ""
"plugin active to see the full list of theme options. You can see if it is "
"enabled via Appearance → Install Plugins."
msgstr ""

#: inc/setup/sections/intro.php:38
msgid "Launch the Customizer"
msgstr ""

#: inc/setup/sections/intro.php:50
msgid "demir WooCommerce Theme"
msgstr ""

#: inc/setup/sections/intro.php:51
msgid ""
"More information about demir including FAQs, guides and tutorials on "
"optimizing your WooCommerce store."
msgstr ""

#: inc/setup/sections/intro.php:53
msgid "demir Information"
msgstr ""

#: inc/setup/sections/intro.php:58
msgid "Support"
msgstr ""

#: inc/setup/sections/intro.php:59
msgid ""
"Have any questions or need help? Get in touch with our support desk for "
"assistance. You will need to include your order number, a link to your site "
"and a screenshot of the issue."
msgstr ""

#: inc/setup/sections/intro.php:61
msgid "Submit a Ticket"
msgstr ""

#: inc/setup/sections/usefulplugins.php:17
msgid ""
"Enhance your store with these useful optional plugin suggestions for "
"demir. Just search the \"Plugins\" section of WordPress for the name, "
"then install and activate. You will need to consult the plugin documentation "
"of each for setup instructions."
msgstr ""

#: inc/setup/sections/usefulplugins.php:24
msgid "Autoptimize"
msgstr ""

#: inc/setup/sections/usefulplugins.php:25
msgid ""
"Optimizes your website, concatenating the CSS and JavaScript code, and "
"compressing it."
msgstr ""

#: inc/setup/sections/usefulplugins.php:28
#: inc/setup/sections/usefulplugins.php:37
#: inc/setup/sections/usefulplugins.php:46
#: inc/setup/sections/usefulplugins.php:55
#: inc/setup/sections/usefulplugins.php:64
#: inc/setup/sections/usefulplugins.php:73
#: inc/setup/sections/usefulplugins.php:82
#: inc/setup/sections/usefulplugins.php:91
#: inc/setup/sections/usefulplugins.php:100
#: inc/setup/sections/usefulplugins.php:109
#: inc/setup/sections/usefulplugins.php:118
#: inc/setup/sections/usefulplugins.php:127
msgid "More information"
msgstr ""

#: inc/setup/sections/usefulplugins.php:33
msgid "Jetpack"
msgstr ""

#: inc/setup/sections/usefulplugins.php:34
msgid ""
"The popular plugin from Automattic has some useful features worth enabling, "
"including lazy load and  photon for quicker page loading times. We are also "
"using the related posts module on our demo site."
msgstr ""

#: inc/setup/sections/usefulplugins.php:42
msgid "Loco Translate"
msgstr ""

#: inc/setup/sections/usefulplugins.php:43
msgid ""
"Loco Translate provides in-browser editing of WordPress translation files. "
"It is the easiest way to change your store language to something else."
msgstr ""

#: inc/setup/sections/usefulplugins.php:51
msgid "MailChimp for WordPress"
msgstr ""

#: inc/setup/sections/usefulplugins.php:52
msgid ""
"Allows visitors to subscribe to your newsletters easily. Requires a free "
"MailChimp account."
msgstr ""

#: inc/setup/sections/usefulplugins.php:60
msgid "Optin Forms"
msgstr ""

#: inc/setup/sections/usefulplugins.php:61
msgid ""
"Create optin forms without the need to know any HTML or CSS. Works with a "
"number of different email solutions including MailChimp and ConvertKit."
msgstr ""

#: inc/setup/sections/usefulplugins.php:69
msgid "Real-Time Find and Replace"
msgstr ""

#: inc/setup/sections/usefulplugins.php:70
msgid ""
"This plugin allows you to dynamically replace text with alternative copy of "
"your choosing before a page is delivered to a user’s browser."
msgstr ""

#: inc/setup/sections/usefulplugins.php:78
msgid "Smart WooCommerce Search"
msgstr ""

#: inc/setup/sections/usefulplugins.php:79
msgid ""
"Provides instant search results for your WooCommerce website when a user "
"types some characters."
msgstr ""

#: inc/setup/sections/usefulplugins.php:87
msgid "Woo Advanced Product Size Chart"
msgstr ""

#: inc/setup/sections/usefulplugins.php:88
msgid ""
"Add product size charts with default template or custom size chart to any of "
"your WooCommerce products."
msgstr ""

#: inc/setup/sections/usefulplugins.php:96
msgid "WooCommerce Product Tabs"
msgstr ""

#: inc/setup/sections/usefulplugins.php:97
msgid "Helps you add your own custom tabs to the product page in WooCommerce."
msgstr ""

#: inc/setup/sections/usefulplugins.php:105
msgid "Variation Swatches for WooCommerce"
msgstr ""

#: inc/setup/sections/usefulplugins.php:106
msgid ""
"A much nicer way to display variations of variable products. This plugin "
"will help you select a style for each attribute as a color, image or label."
msgstr ""

#: inc/setup/sections/usefulplugins.php:114
msgid "Weglot"
msgstr ""

#: inc/setup/sections/usefulplugins.php:115
msgid ""
"The best and easiest translation solution to translate your demir "
"store and go multilingual. You can be setup in minutes. Need more languages?"
msgstr ""

#: inc/setup/sections/usefulplugins.php:115
msgid "See the premium version"
msgstr ""

#: inc/setup/sections/usefulplugins.php:123
msgid "WPForms"
msgstr ""

#: inc/setup/sections/usefulplugins.php:124
msgid ""
"WPForms allows you to create contact forms, and any other kind of form on "
"your site in minutes."
msgstr ""

#: inc/demir-template-functions.php:46
msgid "Your comment is awaiting moderation."
msgstr ""

#: inc/demir-template-functions.php:77
msgid "Edit"
msgstr ""

#: inc/demir-template-functions.php:495
msgid "Primary Navigation"
msgstr ""

#: inc/demir-template-functions.php:614
msgid "Skip to navigation"
msgstr ""

#: inc/demir-template-functions.php:615
msgid "Skip to content"
msgstr ""

#: inc/demir-template-functions.php:653
#: inc/demir-template-functions.php:759
msgid "Pages:"
msgstr ""

#: inc/demir-template-functions.php:750
#, php-format
msgid "Continue reading %s"
msgstr ""

#: inc/demir-template-functions.php:815
#: inc/demir-template-functions.php:829
msgid ", "
msgstr ""

#: inc/demir-template-functions.php:821
msgid "Posted in:"
msgstr ""

#: inc/demir-template-functions.php:835
msgid "Tagged:"
msgstr ""

#: inc/demir-template-functions.php:856
msgid "Previous article"
msgstr ""

#: inc/demir-template-functions.php:862
msgid "Next article"
msgstr ""

#: inc/woocommerce/demir-woocommerce-template-functions.php:176
#: inc/woocommerce/demir-woocommerce-template-functions.php:207
msgid "Below Category Content"
msgstr ""

#: inc/woocommerce/demir-woocommerce-template-functions.php:178
#: inc/woocommerce/demir-woocommerce-template-functions.php:211
msgid "Category information which appears below the product list"
msgstr ""

#: inc/woocommerce/demir-woocommerce-template-functions.php:540
#: inc/woocommerce/demir-woocommerce-template-functions.php:542
msgid "View your shopping cart"
msgstr ""

#: inc/woocommerce/demir-woocommerce-template-functions.php:1100
msgid "Out of stock"
msgstr ""

#: inc/woocommerce/demir-woocommerce-template-functions.php:1146
#, php-format
msgid "<span class=\"sale-item product-label\">-%s</span>"
msgstr ""

#: inc/woocommerce/demir-woocommerce-template-functions.php:1711
msgid "Show Filters"
msgstr ""

#: inc/woocommerce/demir-woocommerce-template-functions.php:1830
msgid "Checkout"
msgstr ""

#: inc/woocommerce/demir-woocommerce-template-functions.php:1832
msgid "View Cart"
msgstr ""

#: inc/woocommerce/demir-woocommerce-template-functions.php:1991
msgid "Shopping Cart"
msgstr ""

#: inc/woocommerce/demir-woocommerce-template-functions.php:1995
msgid "Shipping and Checkout"
msgstr ""

#: inc/woocommerce/demir-woocommerce-template-functions.php:1996
msgid "Confirmation"
msgstr ""

#: inc/woocommerce/demir-woocommerce-template-functions.php:2377
msgid "Select options"
msgstr ""

#: preloadcss.php:270
msgid ":preload"
msgstr ""

#: search.php:16
#, php-format
msgid "Search Results for: %s"
msgstr ""

