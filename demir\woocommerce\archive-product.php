<?php
/**
 * The Template for displaying product archives, including the main shop page which is a post type archive
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/archive-product.php.
 *
 * @package trendkurs
 */

defined( 'ABSPATH' ) || exit;

get_header( 'shop' ); ?>

<div class="trendkurs-shop-page" style="padding: 40px 0; background: #f8f9fa;">
    <div class="container">
        
        <!-- Shop Header -->
        <div class="shop-header" style="background: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 20px;">
                
                <!-- Page Title -->
                <div class="shop-title">
                    <?php if ( apply_filters( 'woocommerce_show_page_title', true ) ) : ?>
                        <h1 class="woocommerce-products-header__title page-title" style="font-size: 32px; color: #333; margin: 0;">
                            <?php woocommerce_page_title(); ?>
                        </h1>
                    <?php endif; ?>
                    
                    <?php
                    /**
                     * Hook: woocommerce_archive_description.
                     *
                     * @hooked woocommerce_taxonomy_archive_description - 10
                     * @hooked woocommerce_product_archive_description - 10
                     */
                    do_action( 'woocommerce_archive_description' );
                    ?>
                </div>
                
                <!-- Results Count & Ordering -->
                <div class="shop-controls" style="display: flex; align-items: center; gap: 20px; flex-wrap: wrap;">
                    <?php
                    /**
                     * Hook: woocommerce_before_shop_loop.
                     *
                     * @hooked woocommerce_output_all_notices - 10
                     * @hooked woocommerce_result_count - 20
                     * @hooked woocommerce_catalog_ordering - 30
                     */
                    do_action( 'woocommerce_before_shop_loop' );
                    ?>
                </div>
                
            </div>
        </div>

        <div class="shop-content" style="display: grid; grid-template-columns: 250px 1fr; gap: 30px;">
            
            <!-- Sidebar Filters -->
            <aside class="shop-sidebar" style="background: white; padding: 25px; border-radius: 12px; height: fit-content; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                <h3 style="color: #333; margin-bottom: 20px; font-size: 18px;">Filtreler</h3>
                
                <?php
                /**
                 * Hook: woocommerce_sidebar.
                 *
                 * @hooked woocommerce_get_sidebar - 10
                 */
                if ( is_active_sidebar( 'sidebar-shop' ) ) {
                    dynamic_sidebar( 'sidebar-shop' );
                } else {
                    // Default filters if no sidebar widgets
                    echo '<div class="default-filters">';
                    echo '<h4 style="margin-bottom: 15px;">Kategoriler</h4>';
                    
                    $categories = get_terms( array(
                        'taxonomy' => 'product_cat',
                        'hide_empty' => true,
                        'parent' => 0,
                        'number' => 10
                    ) );
                    
                    if ( $categories ) {
                        echo '<ul style="list-style: none; padding: 0;">';
                        foreach ( $categories as $category ) {
                            echo '<li style="margin-bottom: 8px;">';
                            echo '<a href="' . esc_url( get_term_link( $category ) ) . '" style="color: #666; text-decoration: none; display: block; padding: 5px 0; border-bottom: 1px solid #f0f0f0;">';
                            echo esc_html( $category->name ) . ' (' . $category->count . ')';
                            echo '</a>';
                            echo '</li>';
                        }
                        echo '</ul>';
                    }
                    echo '</div>';
                }
                ?>
            </aside>

            <!-- Products Grid -->
            <main class="shop-main">
                
                <?php if ( woocommerce_product_loop() ) : ?>

                    <div class="products-container">
                        
                        <?php
                        woocommerce_product_loop_start();

                        if ( wc_get_loop_prop( 'is_shortcode' ) ) {
                            $columns = absint( wc_get_loop_prop( 'columns' ) );
                        } else {
                            $columns = wc_get_default_products_per_row();
                        }
                        ?>

                        <div class="products-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 20px;">
                            
                            <?php
                            if ( woocommerce_product_loop() ) {
                                while ( have_posts() ) {
                                    the_post();

                                    /**
                                     * Hook: woocommerce_shop_loop.
                                     */
                                    do_action( 'woocommerce_shop_loop' );

                                    wc_get_template_part( 'content', 'product' );
                                }
                            }
                            ?>
                            
                        </div>

                        <?php
                        woocommerce_product_loop_end();

                        /**
                         * Hook: woocommerce_after_shop_loop.
                         *
                         * @hooked woocommerce_pagination - 10
                         */
                        do_action( 'woocommerce_after_shop_loop' );
                        ?>
                        
                    </div>

                <?php else : ?>

                    <div class="no-products-found" style="text-align: center; padding: 60px 20px; background: white; border-radius: 12px;">
                        <h2 style="color: #666; margin-bottom: 20px;">Urun bulunamadi</h2>
                        <p style="color: #999; margin-bottom: 30px;">Aradiginiz kriterlere uygun urun bulunamadi.</p>
                        <a href="<?php echo esc_url( wc_get_page_permalink( 'shop' ) ); ?>" class="btn">Tum Urunleri Gor</a>
                    </div>

                    <?php
                    /**
                     * Hook: woocommerce_no_products_found.
                     *
                     * @hooked wc_no_products_found - 10
                     */
                    do_action( 'woocommerce_no_products_found' );
                    ?>

                <?php endif; ?>
                
            </main>
            
        </div>
        
    </div>
</div>

<!-- Responsive Styles -->
<style>
@media (max-width: 768px) {
    .shop-content {
        grid-template-columns: 1fr !important;
    }
    
    .shop-sidebar {
        order: 2;
    }
    
    .shop-main {
        order: 1;
    }
    
    .products-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
    }
    
    .shop-controls {
        justify-content: center !important;
    }
}

@media (max-width: 480px) {
    .products-grid {
        grid-template-columns: 1fr !important;
    }
    
    .shop-header {
        padding: 20px !important;
    }
    
    .shop-sidebar {
        padding: 20px !important;
    }
}
</style>

<?php
/**
 * Hook: woocommerce_after_main_content.
 *
 * @hooked woocommerce_output_content_wrapper_end - 10 (outputs closing divs for the content)
 */
do_action( 'woocommerce_after_main_content' );

get_footer( 'shop' );
?>
